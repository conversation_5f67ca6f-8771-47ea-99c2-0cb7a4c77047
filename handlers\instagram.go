package handlers

import (
	"net/http"
	"strconv"

	"recovery-dashboard/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// InstagramHandler handles Instagram-specific operations
type InstagramHandler struct {
	db                    *gorm.DB
	instagramService      *services.InstagramService
	visualContentService  *services.VisualContentService
}

// NewInstagramHandler creates a new Instagram handler
func NewInstagramHandler(db *gorm.DB, instagramService *services.InstagramService, visualContentService *services.VisualContentService) *InstagramHandler {
	return &InstagramHandler{
		db:                   db,
		instagramService:     instagramService,
		visualContentService: visualContentService,
	}
}

// GetUserInfo retrieves Instagram user information
func (h *InstagramHandler) GetUserInfo(c *gin.Context) {
	if h.instagramService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Instagram service not available"})
		return
	}

	userInfo, err := h.instagramService.GetUserInfo()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user": userInfo,
		"status": "success",
	})
}

// GetUserMedia retrieves user's Instagram media
func (h *InstagramHandler) GetUserMedia(c *gin.Context) {
	if h.instagramService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Instagram service not available"})
		return
	}

	limitStr := c.DefaultQuery("limit", "25")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 25
	}

	media, err := h.instagramService.GetUserMedia(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get media: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"media": media,
		"count": len(media),
		"status": "success",
	})
}

// CreatePost creates a new Instagram post
func (h *InstagramHandler) CreatePost(c *gin.Context) {
	if h.instagramService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Instagram service not available"})
		return
	}

	var req struct {
		Caption     string `json:"caption" binding:"required"`
		ImageURL    string `json:"image_url"`
		VideoURL    string `json:"video_url"`
		MediaType   string `json:"media_type"` // IMAGE, VIDEO
		AccountType string `json:"account_type"` // "merch", "memes", "serious"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default media type
	if req.MediaType == "" {
		if req.VideoURL != "" {
			req.MediaType = "VIDEO"
		} else {
			req.MediaType = "IMAGE"
		}
	}

	// Validate media URL
	if req.ImageURL == "" && req.VideoURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Either image_url or video_url is required"})
		return
	}

	postReq := services.InstagramCreatePostRequest{
		Caption:   req.Caption,
		ImageURL:  req.ImageURL,
		VideoURL:  req.VideoURL,
		MediaType: req.MediaType,
	}

	postID, err := h.instagramService.CreatePost(postReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create post: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"post_id": postID,
		"message": "Instagram post created successfully",
	})
}

// CrossPostYouTubeVideo creates an Instagram post from a YouTube video
func (h *InstagramHandler) CrossPostYouTubeVideo(c *gin.Context) {
	if h.instagramService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Instagram service not available"})
		return
	}

	var req struct {
		VideoID      string `json:"video_id" binding:"required"`
		VideoTitle   string `json:"video_title" binding:"required"`
		VideoURL     string `json:"video_url" binding:"required"`
		SpeakerName  string `json:"speaker_name"`
		AccountType  string `json:"account_type"` // "merch", "memes", "serious"
		GenerateVisual bool `json:"generate_visual"` // Whether to generate visual content
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "merch"
	}

	var imageURL string
	var err error

	// Generate visual content if requested and service is available
	if req.GenerateVisual && h.visualContentService != nil {
		generatedImage, genErr := h.visualContentService.GenerateYouTubeVideoThumbnail(
			req.VideoTitle,
			req.SpeakerName,
			req.AccountType,
		)
		if genErr != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to generate visual content: " + genErr.Error(),
			})
			return
		}
		imageURL = generatedImage.URL
	}

	// Generate Instagram-appropriate content
	postContent := h.instagramService.GenerateRecoveryPost(req.VideoTitle, req.VideoURL, req.AccountType)
	
	// Set the generated image URL
	if imageURL != "" {
		postContent.ImageURL = imageURL
	}

	// Create the post
	postID, err := h.instagramService.CreatePost(postContent)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cross-post video: " + err.Error()})
		return
	}

	response := gin.H{
		"success":            true,
		"post_id":            postID,
		"video_id":           req.VideoID,
		"instagram_post_id":  postID,
		"message":            "YouTube video cross-posted to Instagram successfully",
		"generated_content": gin.H{
			"caption":    postContent.Caption,
			"media_type": postContent.MediaType,
		},
	}

	if imageURL != "" {
		response["generated_image"] = imageURL
	}

	c.JSON(http.StatusOK, response)
}

// CreateRecoveryQuotePost creates a recovery quote post with visual content
func (h *InstagramHandler) CreateRecoveryQuotePost(c *gin.Context) {
	if h.instagramService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Instagram service not available"})
		return
	}

	var req struct {
		Quote       string `json:"quote" binding:"required"`
		Author      string `json:"author" binding:"required"`
		AccountType string `json:"account_type"` // "merch", "memes", "serious"
		ImageURL    string `json:"image_url"`    // Optional: provide custom image
		GenerateImage bool `json:"generate_image"` // Whether to generate image
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "serious"
	}

	var imageURL = req.ImageURL

	// Generate visual content if requested and no image provided
	if req.GenerateImage && imageURL == "" && h.visualContentService != nil {
		generatedImage, err := h.visualContentService.GenerateRecoveryQuoteForPlatform(
			req.Quote,
			req.Author,
			"instagram",
			req.AccountType,
		)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to generate quote image: " + err.Error(),
			})
			return
		}
		imageURL = generatedImage.URL
	}

	// Validate that we have an image
	if imageURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Image URL required - either provide image_url or set generate_image to true",
		})
		return
	}

	// Create the recovery quote post
	postID, err := h.instagramService.CreateRecoveryQuotePost(req.Quote, req.Author, imageURL, req.AccountType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create quote post: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"post_id":  postID,
		"image_url": imageURL,
		"message":  "Recovery quote post created successfully",
	})
}

// GetAnalytics retrieves Instagram analytics
func (h *InstagramHandler) GetAnalytics(c *gin.Context) {
	if h.instagramService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Instagram service not available"})
		return
	}

	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		days = 30
	}

	analytics, err := h.instagramService.GetAnalytics(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get analytics: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"analytics":   analytics,
		"period_days": days,
		"status":      "success",
	})
}

// ValidateToken checks if the Instagram access token is valid
func (h *InstagramHandler) ValidateToken(c *gin.Context) {
	if h.instagramService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Instagram service not available"})
		return
	}

	err := h.instagramService.ValidateToken()
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid": false,
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":   true,
		"message": "Instagram token is valid",
	})
}
