package services

import (
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/png"
	"os"
	"path/filepath"
	"strings"
	"time"

	"golang.org/x/image/font"
	"golang.org/x/image/font/basicfont"
	"golang.org/x/image/math/fixed"
)

// VisualContentService handles visual content generation
type VisualContentService struct {
	OutputDir   string
	BaseURL     string // For serving generated images
	FontPath    string
	BrandColors map[string]color.RGBA
}

// NewVisualContentService creates a new visual content service
func NewVisualContentService(outputDir, baseURL string) *VisualContentService {
	// Create output directory if it doesn't exist
	os.MkdirAll(outputDir, 0755)

	return &VisualContentService{
		OutputDir: outputDir,
		BaseURL:   baseURL,
		BrandColors: map[string]color.RGBA{
			"primary":   {R: 74, G: 144, B: 226, A: 255},  // Blue
			"secondary": {R: 46, G: 204, B: 113, A: 255},  // Green
			"accent":    {R: 231, G: 76, B: 60, A: 255},   // Red
			"dark":      {R: 52, G: 73, B: 94, A: 255},    // Dark Gray
			"light":     {R: 236, G: 240, B: 241, A: 255}, // Light Gray
			"white":     {R: 255, G: 255, B: 255, A: 255}, // White
		},
	}
}

// RecoveryQuoteImage represents a recovery quote image configuration
type RecoveryQuoteImage struct {
	Quote       string
	Author      string
	Background  string // "gradient", "solid", "pattern"
	ColorScheme string // "primary", "secondary", "accent"
	Width       int
	Height      int
	FontSize    int
	AccountType string // "merch", "memes", "serious"
}

// SpeakerHighlightImage represents a speaker highlight image configuration
type SpeakerHighlightImage struct {
	SpeakerName string
	VideoTitle  string
	Duration    string
	Thumbnail   string // URL to video thumbnail
	Background  string
	ColorScheme string
	Width       int
	Height      int
	AccountType string
}

// MerchandisePromoImage represents a merchandise promotion image configuration
type MerchandisePromoImage struct {
	ProductName string
	Description string
	Price       string
	ProductURL  string
	Background  string
	ColorScheme string
	Width       int
	Height      int
}

// GeneratedImage represents a generated image result
type GeneratedImage struct {
	Filename string `json:"filename"`
	URL      string `json:"url"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
	Format   string `json:"format"`
}

// GenerateRecoveryQuote creates a recovery quote image
func (vcs *VisualContentService) GenerateRecoveryQuote(config RecoveryQuoteImage) (*GeneratedImage, error) {
	// Set defaults
	if config.Width == 0 {
		config.Width = 1080
	}
	if config.Height == 0 {
		config.Height = 1080
	}
	if config.FontSize == 0 {
		config.FontSize = 48
	}

	// Create image
	img := image.NewRGBA(image.Rect(0, 0, config.Width, config.Height))

	// Set background color based on color scheme
	bgColor := vcs.BrandColors["primary"]
	if color, exists := vcs.BrandColors[config.ColorScheme]; exists {
		bgColor = color
	}

	// Fill background
	draw.Draw(img, img.Bounds(), &image.Uniform{bgColor}, image.Point{}, draw.Src)

	// Add text (simplified - in production would use proper font rendering)
	vcs.addTextToImage(img, config.Quote, config.Author, config.Width, config.Height)

	// Generate filename
	timestamp := time.Now().Unix()
	filename := fmt.Sprintf("quote_%d.png", timestamp)
	filepath := filepath.Join(vcs.OutputDir, filename)

	// Save image
	file, err := os.Create(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to create image file: %w", err)
	}
	defer file.Close()

	if err := png.Encode(file, img); err != nil {
		return nil, fmt.Errorf("failed to encode image: %w", err)
	}

	return &GeneratedImage{
		Filename: filename,
		URL:      fmt.Sprintf("%s/%s", vcs.BaseURL, filename),
		Width:    config.Width,
		Height:   config.Height,
		Format:   "png",
	}, nil
}

// GenerateSpeakerHighlight creates a speaker highlight image
func (vcs *VisualContentService) GenerateSpeakerHighlight(config SpeakerHighlightImage) (*GeneratedImage, error) {
	// Set defaults
	if config.Width == 0 {
		config.Width = 1080
	}
	if config.Height == 0 {
		config.Height = 1080
	}

	// Create image
	img := image.NewRGBA(image.Rect(0, 0, config.Width, config.Height))

	// Set background
	bgColor := vcs.BrandColors["secondary"]
	if color, exists := vcs.BrandColors[config.ColorScheme]; exists {
		bgColor = color
	}

	// Fill background
	draw.Draw(img, img.Bounds(), &image.Uniform{bgColor}, image.Point{}, draw.Src)

	// Add speaker info text
	speakerText := fmt.Sprintf("Speaker: %s", config.SpeakerName)
	titleText := config.VideoTitle
	durationText := fmt.Sprintf("Duration: %s", config.Duration)

	vcs.addSpeakerTextToImage(img, speakerText, titleText, durationText, config.Width, config.Height)

	// Generate filename
	timestamp := time.Now().Unix()
	filename := fmt.Sprintf("speaker_%d.png", timestamp)
	filepath := filepath.Join(vcs.OutputDir, filename)

	// Save image
	file, err := os.Create(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to create image file: %w", err)
	}
	defer file.Close()

	if err := png.Encode(file, img); err != nil {
		return nil, fmt.Errorf("failed to encode image: %w", err)
	}

	return &GeneratedImage{
		Filename: filename,
		URL:      fmt.Sprintf("%s/%s", vcs.BaseURL, filename),
		Width:    config.Width,
		Height:   config.Height,
		Format:   "png",
	}, nil
}

// GenerateMerchandisePromo creates a merchandise promotion image
func (vcs *VisualContentService) GenerateMerchandisePromo(config MerchandisePromoImage) (*GeneratedImage, error) {
	// Set defaults
	if config.Width == 0 {
		config.Width = 1080
	}
	if config.Height == 0 {
		config.Height = 1080
	}

	// Create image
	img := image.NewRGBA(image.Rect(0, 0, config.Width, config.Height))

	// Set background
	bgColor := vcs.BrandColors["accent"]
	if color, exists := vcs.BrandColors[config.ColorScheme]; exists {
		bgColor = color
	}

	// Fill background
	draw.Draw(img, img.Bounds(), &image.Uniform{bgColor}, image.Point{}, draw.Src)

	// Add merchandise info text
	productText := config.ProductName
	descText := config.Description
	priceText := config.Price

	vcs.addMerchTextToImage(img, productText, descText, priceText, config.Width, config.Height)

	// Generate filename
	timestamp := time.Now().Unix()
	filename := fmt.Sprintf("merch_%d.png", timestamp)
	filepath := filepath.Join(vcs.OutputDir, filename)

	// Save image
	file, err := os.Create(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to create image file: %w", err)
	}
	defer file.Close()

	if err := png.Encode(file, img); err != nil {
		return nil, fmt.Errorf("failed to encode image: %w", err)
	}

	return &GeneratedImage{
		Filename: filename,
		URL:      fmt.Sprintf("%s/%s", vcs.BaseURL, filename),
		Width:    config.Width,
		Height:   config.Height,
		Format:   "png",
	}, nil
}

// Helper function to add text to image (simplified implementation)
func (vcs *VisualContentService) addTextToImage(img *image.RGBA, quote, author string, width, height int) {
	// This is a simplified text rendering - in production, you'd use a proper font library
	// For now, we'll use basic font rendering

	col := color.RGBA{255, 255, 255, 255} // White text
	point := fixed.Point26_6{
		X: fixed.I(50),
		Y: fixed.I(height / 2),
	}

	d := &font.Drawer{
		Dst:  img,
		Src:  image.NewUniform(col),
		Face: basicfont.Face7x13,
		Dot:  point,
	}

	// Wrap text for quote
	wrappedQuote := vcs.wrapText(quote, 60)
	lines := strings.Split(wrappedQuote, "\n")

	for i, line := range lines {
		d.Dot = fixed.Point26_6{
			X: fixed.I(50),
			Y: fixed.I(200 + i*30),
		}
		d.DrawString(line)
	}

	// Add author
	d.Dot = fixed.Point26_6{
		X: fixed.I(width - 200),
		Y: fixed.I(height - 100),
	}
	d.DrawString("- " + author)
}

// Helper function to add speaker text to image
func (vcs *VisualContentService) addSpeakerTextToImage(img *image.RGBA, speaker, title, duration string, width, height int) {
	col := color.RGBA{255, 255, 255, 255}
	d := &font.Drawer{
		Dst:  img,
		Src:  image.NewUniform(col),
		Face: basicfont.Face7x13,
	}

	// Speaker name
	d.Dot = fixed.Point26_6{X: fixed.I(50), Y: fixed.I(150)}
	d.DrawString(speaker)

	// Title (wrapped)
	wrappedTitle := vcs.wrapText(title, 50)
	lines := strings.Split(wrappedTitle, "\n")
	for i, line := range lines {
		d.Dot = fixed.Point26_6{
			X: fixed.I(50),
			Y: fixed.I(250 + i*25),
		}
		d.DrawString(line)
	}

	// Duration
	d.Dot = fixed.Point26_6{X: fixed.I(50), Y: fixed.I(height - 100)}
	d.DrawString(duration)
}

// Helper function to add merchandise text to image
func (vcs *VisualContentService) addMerchTextToImage(img *image.RGBA, product, desc, price string, width, height int) {
	col := color.RGBA{255, 255, 255, 255}
	d := &font.Drawer{
		Dst:  img,
		Src:  image.NewUniform(col),
		Face: basicfont.Face7x13,
	}

	// Product name
	d.Dot = fixed.Point26_6{X: fixed.I(50), Y: fixed.I(150)}
	d.DrawString(product)

	// Description (wrapped)
	wrappedDesc := vcs.wrapText(desc, 50)
	lines := strings.Split(wrappedDesc, "\n")
	for i, line := range lines {
		d.Dot = fixed.Point26_6{
			X: fixed.I(50),
			Y: fixed.I(250 + i*25),
		}
		d.DrawString(line)
	}

	// Price
	d.Dot = fixed.Point26_6{X: fixed.I(width - 150), Y: fixed.I(height - 100)}
	d.DrawString(price)
}

// Helper function to wrap text
func (vcs *VisualContentService) wrapText(text string, maxWidth int) string {
	words := strings.Fields(text)
	if len(words) == 0 {
		return text
	}

	var lines []string
	var currentLine string

	for _, word := range words {
		if len(currentLine)+len(word)+1 <= maxWidth {
			if currentLine == "" {
				currentLine = word
			} else {
				currentLine += " " + word
			}
		} else {
			if currentLine != "" {
				lines = append(lines, currentLine)
			}
			currentLine = word
		}
	}

	if currentLine != "" {
		lines = append(lines, currentLine)
	}

	return strings.Join(lines, "\n")
}

// GenerateYouTubeVideoThumbnail creates a thumbnail image for YouTube video cross-posting
func (vcs *VisualContentService) GenerateYouTubeVideoThumbnail(videoTitle, speakerName, accountType string) (*GeneratedImage, error) {
	config := SpeakerHighlightImage{
		SpeakerName: speakerName,
		VideoTitle:  videoTitle,
		Duration:    "Recovery Content",
		Width:       1080,
		Height:      1080,
		AccountType: accountType,
	}

	// Set color scheme based on account type
	switch accountType {
	case "merch":
		config.ColorScheme = "accent"
	case "memes":
		config.ColorScheme = "secondary"
	case "serious":
		config.ColorScheme = "primary"
	default:
		config.ColorScheme = "primary"
	}

	return vcs.GenerateSpeakerHighlight(config)
}

// GenerateRecoveryQuoteForPlatform creates a recovery quote image optimized for specific platforms
func (vcs *VisualContentService) GenerateRecoveryQuoteForPlatform(quote, author, platform, accountType string) (*GeneratedImage, error) {
	config := RecoveryQuoteImage{
		Quote:       quote,
		Author:      author,
		AccountType: accountType,
	}

	// Platform-specific dimensions
	switch platform {
	case "instagram":
		config.Width = 1080
		config.Height = 1080
	case "pinterest":
		config.Width = 735
		config.Height = 1102 // Pinterest optimal ratio
	case "facebook":
		config.Width = 1200
		config.Height = 630
	default:
		config.Width = 1080
		config.Height = 1080
	}

	// Account type specific styling
	switch accountType {
	case "merch":
		config.ColorScheme = "accent"
		config.Background = "gradient"
	case "memes":
		config.ColorScheme = "secondary"
		config.Background = "solid"
	case "serious":
		config.ColorScheme = "primary"
		config.Background = "gradient"
	default:
		config.ColorScheme = "primary"
		config.Background = "solid"
	}

	return vcs.GenerateRecoveryQuote(config)
}

// GenerateMerchandisePromoForPlatform creates merchandise promotion images optimized for platforms
func (vcs *VisualContentService) GenerateMerchandisePromoForPlatform(productName, description, price, platform string) (*GeneratedImage, error) {
	config := MerchandisePromoImage{
		ProductName: productName,
		Description: description,
		Price:       price,
		ColorScheme: "accent",
	}

	// Platform-specific dimensions
	switch platform {
	case "instagram":
		config.Width = 1080
		config.Height = 1080
	case "pinterest":
		config.Width = 735
		config.Height = 1102
	case "facebook":
		config.Width = 1200
		config.Height = 630
	default:
		config.Width = 1080
		config.Height = 1080
	}

	return vcs.GenerateMerchandisePromo(config)
}

// GetRecoveryQuotes returns a collection of recovery quotes for content generation
func (vcs *VisualContentService) GetRecoveryQuotes() []struct {
	Quote  string
	Author string
} {
	return []struct {
		Quote  string
		Author string
	}{
		{
			Quote:  "Recovery is not a destination, it's a journey of self-discovery and healing.",
			Author: "Anonymous",
		},
		{
			Quote:  "One day at a time, one step at a time, one breath at a time.",
			Author: "Recovery Wisdom",
		},
		{
			Quote:  "Your story isn't over yet. Keep writing.",
			Author: "Recovery Community",
		},
		{
			Quote:  "Progress, not perfection. Every day is a new beginning.",
			Author: "AA Wisdom",
		},
		{
			Quote:  "The only way out is through. Keep going.",
			Author: "Recovery Journey",
		},
		{
			Quote:  "You are stronger than your addiction. You are worth recovery.",
			Author: "Hope & Healing",
		},
		{
			Quote:  "Recovery is giving yourself permission to live again.",
			Author: "Sobriety Wisdom",
		},
		{
			Quote:  "It's not about being perfect, it's about being present.",
			Author: "Mindful Recovery",
		},
	}
}

// GenerateDailyRecoveryQuote creates a daily recovery quote image
func (vcs *VisualContentService) GenerateDailyRecoveryQuote(platform, accountType string) (*GeneratedImage, error) {
	quotes := vcs.GetRecoveryQuotes()

	// Select quote based on current day (simple rotation)
	dayOfYear := time.Now().YearDay()
	selectedQuote := quotes[dayOfYear%len(quotes)]

	return vcs.GenerateRecoveryQuoteForPlatform(
		selectedQuote.Quote,
		selectedQuote.Author,
		platform,
		accountType,
	)
}

// BatchGenerateContent creates multiple visual content pieces for cross-platform posting
func (vcs *VisualContentService) BatchGenerateContent(videoTitle, speakerName, accountType string) (map[string]*GeneratedImage, error) {
	results := make(map[string]*GeneratedImage)

	// Generate Instagram thumbnail
	instagramImg, err := vcs.GenerateYouTubeVideoThumbnail(videoTitle, speakerName, accountType)
	if err != nil {
		return nil, fmt.Errorf("failed to generate Instagram image: %w", err)
	}
	results["instagram"] = instagramImg

	// Generate Pinterest pin
	pinterestImg, err := vcs.GenerateYouTubeVideoThumbnail(videoTitle, speakerName, accountType)
	if err != nil {
		return nil, fmt.Errorf("failed to generate Pinterest image: %w", err)
	}
	// Adjust for Pinterest dimensions
	pinterestImg.Width = 735
	pinterestImg.Height = 1102
	results["pinterest"] = pinterestImg

	// Generate daily quote for additional content
	dailyQuote, err := vcs.GenerateDailyRecoveryQuote("instagram", accountType)
	if err != nil {
		return nil, fmt.Errorf("failed to generate daily quote: %w", err)
	}
	results["daily_quote"] = dailyQuote

	return results, nil
}

// CleanupOldImages removes images older than specified days
func (vcs *VisualContentService) CleanupOldImages(daysOld int) error {
	cutoffTime := time.Now().AddDate(0, 0, -daysOld)

	return filepath.Walk(vcs.OutputDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && info.ModTime().Before(cutoffTime) {
			// Check if it's an image file
			ext := strings.ToLower(filepath.Ext(path))
			if ext == ".png" || ext == ".jpg" || ext == ".jpeg" {
				return os.Remove(path)
			}
		}

		return nil
	})
}
