package handlers

import (
	"net/http"
	"strconv"

	"recovery-dashboard/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// LinkedInHandler handles LinkedIn-specific operations
type LinkedInHandler struct {
	db              *gorm.DB
	linkedinService *services.LinkedInService
}

// NewLinkedInHandler creates a new LinkedIn handler
func NewLinkedInHandler(db *gorm.DB, linkedinService *services.LinkedInService) *LinkedInHandler {
	return &LinkedInHandler{
		db:              db,
		linkedinService: linkedinService,
	}
}

// GetProfile retrieves LinkedIn profile information
func (h *LinkedInHandler) GetProfile(c *gin.Context) {
	if h.linkedinService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "LinkedIn service not available"})
		return
	}

	profile, err := h.linkedinService.GetProfile()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get profile: " + err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"profile": profile,
		"status":  "success",
	})
}

// CreatePost creates a LinkedIn post/share
func (h *LinkedInHandler) CreatePost(c *gin.Context) {
	if h.linkedinService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "LinkedIn service not available"})
		return
	}

	var req struct {
		Author      string `json:"author" binding:"required"`
		Text        string `json:"text" binding:"required"`
		Visibility  string `json:"visibility"` // PUBLIC, CONNECTIONS
		ContentURL  string `json:"content_url"`
		Title       string `json:"title"`
		Description string `json:"description"`
		ImageURL    string `json:"image_url"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default visibility
	if req.Visibility == "" {
		req.Visibility = "PUBLIC"
	}

	shareReq := services.LinkedInShareRequest{
		Author:      req.Author,
		Text:        req.Text,
		Visibility:  req.Visibility,
		ContentURL:  req.ContentURL,
		Title:       req.Title,
		Description: req.Description,
		ImageURL:    req.ImageURL,
	}

	postID, err := h.linkedinService.CreatePost(shareReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create post: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"post_id": postID,
		"message": "LinkedIn post created successfully",
	})
}

// CreateArticle creates a LinkedIn article
func (h *LinkedInHandler) CreateArticle(c *gin.Context) {
	if h.linkedinService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "LinkedIn service not available"})
		return
	}

	var req struct {
		Title      string   `json:"title" binding:"required"`
		Content    string   `json:"content" binding:"required"`
		Visibility string   `json:"visibility"` // PUBLIC, CONNECTIONS
		Tags       []string `json:"tags"`
		CoverImage string   `json:"cover_image"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default visibility
	if req.Visibility == "" {
		req.Visibility = "PUBLIC"
	}

	articleReq := services.LinkedInArticleRequest{
		Title:      req.Title,
		Content:    req.Content,
		Visibility: req.Visibility,
		Tags:       req.Tags,
		CoverImage: req.CoverImage,
	}

	articleID, err := h.linkedinService.CreateArticle(articleReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create article: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"article_id": articleID,
		"message":    "LinkedIn article created successfully",
	})
}

// CrossPostYouTubeVideo creates a LinkedIn post from a YouTube video
func (h *LinkedInHandler) CrossPostYouTubeVideo(c *gin.Context) {
	if h.linkedinService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "LinkedIn service not available"})
		return
	}

	var req struct {
		VideoID     string `json:"video_id" binding:"required"`
		VideoTitle  string `json:"video_title" binding:"required"`
		VideoURL    string `json:"video_url" binding:"required"`
		AuthorID    string `json:"author_id" binding:"required"`
		AccountType string `json:"account_type"` // "merch", "serious"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "serious"
	}

	// Create the cross-post
	postID, err := h.linkedinService.CrossPostYouTubeVideo(req.VideoTitle, req.VideoURL, req.AuthorID, req.AccountType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cross-post video: " + err.Error()})
		return
	}

	// Generate preview of what was posted
	postContent := h.linkedinService.GenerateRecoveryPost(req.VideoTitle, req.VideoURL, req.AuthorID, req.AccountType)

	c.JSON(http.StatusOK, gin.H{
		"success":            true,
		"post_id":            postID,
		"video_id":           req.VideoID,
		"linkedin_post_id":   postID,
		"message":            "YouTube video cross-posted to LinkedIn successfully",
		"generated_content": gin.H{
			"text":        postContent.Text,
			"visibility":  postContent.Visibility,
			"content_url": postContent.ContentURL,
		},
	})
}

// CreateRecoveryArticle creates a professional recovery article
func (h *LinkedInHandler) CreateRecoveryArticle(c *gin.Context) {
	if h.linkedinService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "LinkedIn service not available"})
		return
	}

	var req struct {
		Title       string `json:"title"`
		Topic       string `json:"topic" binding:"required"` // workplace_recovery, leadership_recovery, mental_health
		AccountType string `json:"account_type"`             // "merch", "serious"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "serious"
	}

	// Validate topic
	validTopics := []string{"workplace_recovery", "leadership_recovery", "mental_health", "authenticity", "resilience"}
	validTopic := false
	for _, topic := range validTopics {
		if topic == req.Topic {
			validTopic = true
			break
		}
	}

	if !validTopic {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":        "Invalid topic",
			"valid_topics": validTopics,
		})
		return
	}

	// Create the recovery article
	articleID, err := h.linkedinService.CreateRecoveryArticle(req.Title, req.Topic, req.AccountType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create recovery article: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"article_id": articleID,
		"topic":      req.Topic,
		"message":    "Recovery article created successfully",
	})
}

// GetRecoveryTopics returns professional recovery topics for LinkedIn content
func (h *LinkedInHandler) GetRecoveryTopics(c *gin.Context) {
	if h.linkedinService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "LinkedIn service not available"})
		return
	}

	topics := h.linkedinService.GetRecoveryTopics()

	c.JSON(http.StatusOK, gin.H{
		"topics": topics,
		"count":  len(topics),
		"status": "success",
	})
}

// GetAnalytics retrieves LinkedIn analytics
func (h *LinkedInHandler) GetAnalytics(c *gin.Context) {
	if h.linkedinService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "LinkedIn service not available"})
		return
	}

	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		days = 30
	}

	analytics, err := h.linkedinService.GetAnalytics(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get analytics: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"analytics":   analytics,
		"period_days": days,
		"status":      "success",
	})
}

// ValidateToken checks if the LinkedIn access token is valid
func (h *LinkedInHandler) ValidateToken(c *gin.Context) {
	if h.linkedinService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "LinkedIn service not available"})
		return
	}

	err := h.linkedinService.ValidateToken()
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid": false,
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":   true,
		"message": "LinkedIn token is valid",
	})
}
