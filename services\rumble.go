package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// RumbleService handles Rumble API interactions
type RumbleService struct {
	AccessToken string
	BaseURL     string
	ChannelID   string
}

// NewRumbleService creates a new Rumble service instance
func NewRumbleService(accessToken, channelID string) *RumbleService {
	return &RumbleService{
		AccessToken: accessToken,
		BaseURL:     "https://rumble.com/api/v1",
		ChannelID:   channelID,
	}
}

// RumbleChannel represents Rumble channel information
type RumbleChannel struct {
	ID              string    `json:"id"`
	Name            string    `json:"name"`
	Title           string    `json:"title"`
	Description     string    `json:"description"`
	URL             string    `json:"url"`
	Thumbnail       string    `json:"thumbnail"`
	Banner          string    `json:"banner"`
	SubscriberCount int       `json:"subscriber_count"`
	VideoCount      int       `json:"video_count"`
	ViewCount       int64     `json:"view_count"`
	CreatedAt       time.Time `json:"created_at"`
	IsVerified      bool      `json:"is_verified"`
	IsMonetized     bool      `json:"is_monetized"`
	Category        string    `json:"category"`
}

// RumbleVideo represents a Rumble video
type RumbleVideo struct {
	ID           string    `json:"id"`
	Title        string    `json:"title"`
	Description  string    `json:"description"`
	URL          string    `json:"url"`
	EmbedURL     string    `json:"embed_url"`
	Thumbnail    string    `json:"thumbnail"`
	Duration     int       `json:"duration"` // in seconds
	ViewCount    int64     `json:"view_count"`
	LikeCount    int       `json:"like_count"`
	DislikeCount int       `json:"dislike_count"`
	CommentCount int       `json:"comment_count"`
	ShareCount   int       `json:"share_count"`
	PublishedAt  time.Time `json:"published_at"`
	UpdatedAt    time.Time `json:"updated_at"`
	Status       string    `json:"status"`  // published, processing, failed, etc.
	Privacy      string    `json:"privacy"` // public, unlisted, private
	Tags         []string  `json:"tags"`
	Category     string    `json:"category"`
	Language     string    `json:"language"`
	IsMonetized  bool      `json:"is_monetized"`
	Revenue      float64   `json:"revenue,omitempty"`
	ChannelID    string    `json:"channel_id"`
	ChannelName  string    `json:"channel_name"`
}

// RumbleAnalytics represents Rumble analytics data
type RumbleAnalytics struct {
	TotalVideos      int           `json:"total_videos"`
	TotalViews       int64         `json:"total_views"`
	TotalLikes       int           `json:"total_likes"`
	TotalComments    int           `json:"total_comments"`
	TotalShares      int           `json:"total_shares"`
	TotalRevenue     float64       `json:"total_revenue"`
	AvgViewsPerVideo float64       `json:"avg_views_per_video"`
	EngagementRate   float64       `json:"engagement_rate"`
	MonetizationRate float64       `json:"monetization_rate"`
	SubscriberGrowth int           `json:"subscriber_growth"`
	TopVideos        []RumbleVideo `json:"top_videos"`
}

// RumbleUploadRequest represents a video upload request
type RumbleUploadRequest struct {
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Tags        []string `json:"tags"`
	Category    string   `json:"category"`
	Privacy     string   `json:"privacy"` // public, unlisted, private
	Language    string   `json:"language"`
	Monetize    bool     `json:"monetize"`
	SpeakerID   uint     `json:"speaker_id,omitempty"`
}

// GetChannel retrieves Rumble channel information
func (rs *RumbleService) GetChannel() (*RumbleChannel, error) {
	url := fmt.Sprintf("%s/channels/%s", rs.BaseURL, rs.ChannelID)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+rs.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get channel: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Rumble API error: %s", string(body))
	}

	var channel RumbleChannel
	if err := json.NewDecoder(resp.Body).Decode(&channel); err != nil {
		return nil, fmt.Errorf("failed to decode channel: %w", err)
	}

	return &channel, nil
}

// GetChannelVideos retrieves videos from the channel
func (rs *RumbleService) GetChannelVideos(limit int) ([]RumbleVideo, error) {
	if limit <= 0 || limit > 100 {
		limit = 25
	}

	url := fmt.Sprintf("%s/channels/%s/videos?limit=%d", rs.BaseURL, rs.ChannelID, limit)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+rs.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get videos: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Rumble API error: %s", string(body))
	}

	var response struct {
		Videos []RumbleVideo `json:"videos"`
		Total  int           `json:"total"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode videos: %w", err)
	}

	return response.Videos, nil
}

// UploadVideo uploads a video to Rumble
func (rs *RumbleService) UploadVideo(req RumbleUploadRequest) (string, error) {
	url := fmt.Sprintf("%s/channels/%s/videos", rs.BaseURL, rs.ChannelID)

	jsonBody, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+rs.AccessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to upload video: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Rumble API error: %s", string(body))
	}

	var response struct {
		VideoID string `json:"video_id"`
		URL     string `json:"url"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	return response.VideoID, nil
}

// GetAnalytics retrieves Rumble analytics
func (rs *RumbleService) GetAnalytics(days int) (*RumbleAnalytics, error) {
	// Get channel videos
	videos, err := rs.GetChannelVideos(100)
	if err != nil {
		return nil, err
	}

	// Filter videos from the specified time period
	cutoffTime := time.Now().AddDate(0, 0, -days)
	var recentVideos []RumbleVideo
	var totalViews int64
	var totalLikes, totalComments, totalShares int
	var totalRevenue float64

	for _, video := range videos {
		if video.PublishedAt.After(cutoffTime) {
			recentVideos = append(recentVideos, video)
			totalViews += video.ViewCount
			totalLikes += video.LikeCount
			totalComments += video.CommentCount
			totalShares += video.ShareCount
			totalRevenue += video.Revenue
		}
	}

	// Calculate metrics
	avgViews := float64(0)
	engagementRate := float64(0)
	monetizationRate := float64(0)

	if len(recentVideos) > 0 {
		avgViews = float64(totalViews) / float64(len(recentVideos))

		totalEngagement := totalLikes + totalComments + totalShares
		if totalViews > 0 {
			engagementRate = float64(totalEngagement) / float64(totalViews) * 100
		}

		monetizedVideos := 0
		for _, video := range recentVideos {
			if video.IsMonetized {
				monetizedVideos++
			}
		}
		monetizationRate = float64(monetizedVideos) / float64(len(recentVideos)) * 100
	}

	// Get top performing videos
	topVideos := make([]RumbleVideo, 0)
	for _, video := range recentVideos {
		if video.ViewCount > 1000 { // Threshold for "top" videos
			topVideos = append(topVideos, video)
		}
		if len(topVideos) >= 5 { // Limit to top 5
			break
		}
	}

	analytics := &RumbleAnalytics{
		TotalVideos:      len(recentVideos),
		TotalViews:       totalViews,
		TotalLikes:       totalLikes,
		TotalComments:    totalComments,
		TotalShares:      totalShares,
		TotalRevenue:     totalRevenue,
		AvgViewsPerVideo: avgViews,
		EngagementRate:   engagementRate,
		MonetizationRate: monetizationRate,
		SubscriberGrowth: 25, // Mock data - would come from API
		TopVideos:        topVideos,
	}

	return analytics, nil
}

// ValidateToken checks if the Rumble access token is valid
func (rs *RumbleService) ValidateToken() error {
	_, err := rs.GetChannel()
	return err
}

// CreateRecoveryVideo creates a recovery-focused video upload request for Rumble
func (rs *RumbleService) CreateRecoveryVideo(title, description, speakerName, program, topic string, speakerID uint) RumbleUploadRequest {
	// Generate recovery-specific tags
	tags := []string{
		"recovery",
		"sobriety",
		"addiction recovery",
		"sound of recovery",
		program,
		"recovery speaker",
		"hope",
		"healing",
		"transformation",
		"free speech",
		"authentic stories",
	}

	if program == "AA" {
		tags = append(tags, "alcoholics anonymous", "alcohol recovery", "12 steps")
	} else if program == "NA" {
		tags = append(tags, "narcotics anonymous", "drug recovery", "clean time")
	}

	if topic != "" {
		tags = append(tags, topic)
	}

	// Rumble-specific description with emphasis on authentic recovery stories
	rumbleDescription := fmt.Sprintf("%s\n\n🎯 AUTHENTIC RECOVERY CONTENT\n\nThis is real recovery - no censorship, no algorithms hiding the truth. Just authentic stories of hope, healing, and transformation.\n\n💪 Recovery is possible. Your story matters.\n\n🔗 More recovery resources: [Your Website]\n\n#Recovery #Sobriety #Hope #FreedomOfSpeech #AuthenticStories", description)

	return RumbleUploadRequest{
		Title:       title,
		Description: rumbleDescription,
		Tags:        tags,
		Category:    "Education", // Rumble category for recovery content
		Privacy:     "public",    // Rumble is about open, uncensored content
		Language:    "en",
		Monetize:    true, // Rumble is creator-friendly with monetization
		SpeakerID:   speakerID,
	}
}

// UploadRecoveryVideo uploads a recovery speaker video to Rumble
func (rs *RumbleService) UploadRecoveryVideo(videoPath, title, description, speakerName, program, topic string, speakerID uint) (string, error) {
	// Create recovery-focused upload request
	uploadReq := rs.CreateRecoveryVideo(title, description, speakerName, program, topic, speakerID)

	// Upload the video
	videoID, err := rs.UploadVideo(uploadReq)
	if err != nil {
		return "", fmt.Errorf("failed to upload recovery video: %w", err)
	}

	return videoID, nil
}

// CrossPostFromYouTube creates a Rumble version of a YouTube video
func (rs *RumbleService) CrossPostFromYouTube(youtubeVideoID, youtubeTitle, youtubeDescription string, speakerID uint) (string, error) {
	// Adapt YouTube content for Rumble's free speech audience
	rumbleTitle := fmt.Sprintf("🎯 UNCENSORED: %s", youtubeTitle)
	rumbleDescription := fmt.Sprintf("%s\n\n🚫 NO CENSORSHIP HERE\n\nThis recovery content is shared freely on Rumble - no algorithm suppression, no shadow banning. Just authentic recovery stories reaching the people who need them.\n\n📺 Also available on YouTube: https://youtube.com/watch?v=%s\n\n💪 Recovery is possible. Your story matters.\n\n#Recovery #Sobriety #FreeSpeech #Uncensored #AuthenticStories", youtubeDescription, youtubeVideoID)

	// Create Rumble upload request
	_ = rs.CreateRecoveryVideo(rumbleTitle, rumbleDescription, "", "AA", "", speakerID)

	// In production, this would:
	// 1. Download the video from YouTube (if permitted)
	// 2. Upload to Rumble with free speech emphasis
	// 3. Return the Rumble video ID

	// For now, return a mock video ID
	return "rumble_" + youtubeVideoID, nil
}

// GetRecoveryVideoAnalytics retrieves analytics for recovery videos on Rumble
func (rs *RumbleService) GetRecoveryVideoAnalytics(days int) (map[string]interface{}, error) {
	// Get channel videos
	videos, err := rs.GetChannelVideos(100)
	if err != nil {
		return nil, err
	}

	// Filter recovery videos from the specified time period
	cutoffTime := time.Now().AddDate(0, 0, -days)
	var recoveryVideos []RumbleVideo
	var totalViews int64
	var totalRevenue float64
	var totalEngagement int

	for _, video := range videos {
		if video.PublishedAt.After(cutoffTime) {
			// Check if it's a recovery video by tags or title
			isRecoveryVideo := false
			for _, tag := range video.Tags {
				if tag == "recovery" || tag == "sobriety" {
					isRecoveryVideo = true
					break
				}
			}

			if isRecoveryVideo {
				recoveryVideos = append(recoveryVideos, video)
				totalViews += video.ViewCount
				totalRevenue += video.Revenue
				totalEngagement += video.LikeCount + video.CommentCount + video.ShareCount
			}
		}
	}

	// Calculate recovery-specific metrics
	avgViews := float64(0)
	avgRevenue := float64(0)
	engagementRate := float64(0)

	if len(recoveryVideos) > 0 {
		avgViews = float64(totalViews) / float64(len(recoveryVideos))
		avgRevenue = totalRevenue / float64(len(recoveryVideos))

		if totalViews > 0 {
			engagementRate = float64(totalEngagement) / float64(totalViews) * 100
		}
	}

	analytics := map[string]interface{}{
		"total_recovery_videos": len(recoveryVideos),
		"total_views":           totalViews,
		"total_revenue":         totalRevenue,
		"average_views":         avgViews,
		"average_revenue":       avgRevenue,
		"engagement_rate":       engagementRate,
		"period_days":           days,
		"platform":              "rumble",
		"monetization_friendly": true,
		"censorship_free":       true,
		"creator_revenue_share": "60%", // Rumble's creator-friendly revenue share
		"free_speech_platform":  true,
	}

	return analytics, nil
}

// GetRecoveryCategories returns Rumble categories suitable for recovery content
func (rs *RumbleService) GetRecoveryCategories() []string {
	return []string{
		"Education",
		"Health & Wellness",
		"Lifestyle",
		"Personal Development",
		"Spirituality",
		"Documentary",
		"Inspirational",
		"Self-Help",
	}
}

// GetPlatformAdvantages returns Rumble's advantages for recovery content
func (rs *RumbleService) GetPlatformAdvantages() map[string]interface{} {
	return map[string]interface{}{
		"free_speech": map[string]interface{}{
			"no_censorship":     true,
			"no_shadow_banning": true,
			"authentic_stories": "encouraged",
		},
		"monetization": map[string]interface{}{
			"creator_friendly":  true,
			"revenue_share":     "60%",
			"no_demonetization": "for recovery content",
			"direct_support":    "from viewers",
		},
		"audience": map[string]interface{}{
			"growing_platform":     true,
			"alternative_audience": "seeking authentic content",
			"less_saturated":       "than mainstream platforms",
			"engaged_community":    true,
		},
		"content_freedom": map[string]interface{}{
			"no_algorithm_suppression": true,
			"organic_reach":            "better than mainstream",
			"controversial_topics":     "allowed",
			"authentic_discussions":    "encouraged",
		},
	}
}

// GenerateRumbleOptimizedContent creates Rumble-specific content variations
func (rs *RumbleService) GenerateRumbleOptimizedContent(originalTitle, originalDescription string) map[string]string {
	return map[string]string{
		"title":       fmt.Sprintf("🎯 UNCENSORED: %s", originalTitle),
		"description": fmt.Sprintf("%s\n\n🚫 NO CENSORSHIP • NO ALGORITHM SUPPRESSION\n\nThis is authentic recovery content shared freely on Rumble. No shadow banning, no demonetization - just real stories reaching real people.\n\n💪 Your recovery story matters. Share it freely.\n\n#Recovery #FreeSpeech #Uncensored #AuthenticStories #Rumble", originalDescription),
		"tags":        "recovery,sobriety,free speech,uncensored,authentic stories,hope,healing,transformation",
		"category":    "Education",
		"emphasis":    "Free speech and authentic storytelling",
	}
}
