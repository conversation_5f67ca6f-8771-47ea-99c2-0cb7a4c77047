package handlers

import (
	"net/http"
	"strconv"

	"recovery-dashboard/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// PinterestHandler handles Pinterest-specific operations
type PinterestHandler struct {
	db                    *gorm.DB
	pinterestService      *services.PinterestService
	visualContentService  *services.VisualContentService
}

// NewPinterestHandler creates a new Pinterest handler
func NewPinterestHandler(db *gorm.DB, pinterestService *services.PinterestService, visualContentService *services.VisualContentService) *PinterestHandler {
	return &PinterestHandler{
		db:                   db,
		pinterestService:     pinterestService,
		visualContentService: visualContentService,
	}
}

// GetUserInfo retrieves Pinterest user information
func (h *PinterestHandler) GetUserInfo(c *gin.Context) {
	if h.pinterestService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Pinterest service not available"})
		return
	}

	userInfo, err := h.pinterestService.GetUserInfo()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user": userInfo,
		"status": "success",
	})
}

// GetBoards retrieves user's Pinterest boards
func (h *PinterestHandler) GetBoards(c *gin.Context) {
	if h.pinterestService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Pinterest service not available"})
		return
	}

	boards, err := h.pinterestService.GetBoards()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get boards: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"boards": boards,
		"count": len(boards),
		"status": "success",
	})
}

// CreateBoard creates a new Pinterest board
func (h *PinterestHandler) CreateBoard(c *gin.Context) {
	if h.pinterestService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Pinterest service not available"})
		return
	}

	var req struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
		Privacy     string `json:"privacy"` // PUBLIC, PROTECTED, SECRET
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default privacy
	if req.Privacy == "" {
		req.Privacy = "PUBLIC"
	}

	boardReq := services.PinterestCreateBoardRequest{
		Name:        req.Name,
		Description: req.Description,
		Privacy:     req.Privacy,
	}

	boardID, err := h.pinterestService.CreateBoard(boardReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create board: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"board_id": boardID,
		"message":  "Pinterest board created successfully",
	})
}

// CreatePin creates a new Pinterest pin
func (h *PinterestHandler) CreatePin(c *gin.Context) {
	if h.pinterestService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Pinterest service not available"})
		return
	}

	var req struct {
		BoardID     string `json:"board_id" binding:"required"`
		Title       string `json:"title" binding:"required"`
		Description string `json:"description"`
		Link        string `json:"link"`
		MediaURL    string `json:"media_url" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	pinReq := services.PinterestCreatePinRequest{
		BoardID:     req.BoardID,
		Title:       req.Title,
		Description: req.Description,
		Link:        req.Link,
		MediaURL:    req.MediaURL,
		MediaSource: struct {
			SourceType string `json:"source_type"`
			URL        string `json:"url"`
		}{
			SourceType: "image_url",
			URL:        req.MediaURL,
		},
	}

	pinID, err := h.pinterestService.CreatePin(pinReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create pin: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"pin_id":  pinID,
		"message": "Pinterest pin created successfully",
	})
}

// CrossPostYouTubeVideo creates a Pinterest pin from a YouTube video
func (h *PinterestHandler) CrossPostYouTubeVideo(c *gin.Context) {
	if h.pinterestService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Pinterest service not available"})
		return
	}

	var req struct {
		VideoID       string `json:"video_id" binding:"required"`
		VideoTitle    string `json:"video_title" binding:"required"`
		VideoURL      string `json:"video_url" binding:"required"`
		SpeakerName   string `json:"speaker_name"`
		AccountType   string `json:"account_type"` // "merch", "memes", "serious"
		BoardID       string `json:"board_id"`     // Optional: specific board
		GenerateVisual bool  `json:"generate_visual"` // Whether to generate visual content
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "merch"
	}

	var imageURL string
	var boardID = req.BoardID

	// Generate visual content if requested and service is available
	if req.GenerateVisual && h.visualContentService != nil {
		generatedImage, genErr := h.visualContentService.GenerateYouTubeVideoThumbnail(
			req.VideoTitle,
			req.SpeakerName,
			req.AccountType,
		)
		if genErr != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to generate visual content: " + genErr.Error(),
			})
			return
		}
		imageURL = generatedImage.URL
	}

	// Get or create recovery boards if no specific board provided
	if boardID == "" {
		recoveryBoards, err := h.pinterestService.GetOrCreateRecoveryBoards()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to get recovery boards: " + err.Error(),
			})
			return
		}

		// Select appropriate board based on account type
		switch req.AccountType {
		case "merch":
			boardID = recoveryBoards["recovery_merch"]
		case "memes":
			boardID = recoveryBoards["recovery_humor"]
		case "serious":
			boardID = recoveryBoards["recovery_wisdom"]
		default:
			boardID = recoveryBoards["recovery_stories"]
		}
	}

	// Generate Pinterest-appropriate content
	pinContent := h.pinterestService.GenerateRecoveryPin(req.VideoTitle, req.VideoURL, req.AccountType)
	
	// Set the generated image URL and board ID
	if imageURL != "" {
		pinContent.MediaSource.URL = imageURL
		pinContent.MediaURL = imageURL
	}
	pinContent.BoardID = boardID

	// Create the pin
	pinID, err := h.pinterestService.CreatePin(pinContent)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cross-post video: " + err.Error()})
		return
	}

	response := gin.H{
		"success":           true,
		"pin_id":            pinID,
		"video_id":          req.VideoID,
		"pinterest_pin_id":  pinID,
		"board_id":          boardID,
		"message":           "YouTube video cross-posted to Pinterest successfully",
		"generated_content": gin.H{
			"title":       pinContent.Title,
			"description": pinContent.Description,
			"link":        pinContent.Link,
		},
	}

	if imageURL != "" {
		response["generated_image"] = imageURL
	}

	c.JSON(http.StatusOK, response)
}

// CreateRecoveryQuotePin creates a recovery quote pin with visual content
func (h *PinterestHandler) CreateRecoveryQuotePin(c *gin.Context) {
	if h.pinterestService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Pinterest service not available"})
		return
	}

	var req struct {
		Quote         string `json:"quote" binding:"required"`
		Author        string `json:"author" binding:"required"`
		AccountType   string `json:"account_type"` // "merch", "memes", "serious"
		BoardID       string `json:"board_id"`     // Optional: specific board
		ImageURL      string `json:"image_url"`    // Optional: provide custom image
		GenerateImage bool   `json:"generate_image"` // Whether to generate image
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "serious"
	}

	var imageURL = req.ImageURL
	var boardID = req.BoardID

	// Generate visual content if requested and no image provided
	if req.GenerateImage && imageURL == "" && h.visualContentService != nil {
		generatedImage, err := h.visualContentService.GenerateRecoveryQuoteForPlatform(
			req.Quote,
			req.Author,
			"pinterest",
			req.AccountType,
		)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to generate quote image: " + err.Error(),
			})
			return
		}
		imageURL = generatedImage.URL
	}

	// Get or create recovery boards if no specific board provided
	if boardID == "" {
		recoveryBoards, err := h.pinterestService.GetOrCreateRecoveryBoards()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to get recovery boards: " + err.Error(),
			})
			return
		}
		boardID = recoveryBoards["recovery_quotes"]
	}

	// Validate that we have an image
	if imageURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Image URL required - either provide image_url or set generate_image to true",
		})
		return
	}

	// Create the recovery quote pin
	pinID, err := h.pinterestService.CreateRecoveryQuotePin(req.Quote, req.Author, imageURL, boardID, req.AccountType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create quote pin: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"pin_id":    pinID,
		"board_id":  boardID,
		"image_url": imageURL,
		"message":   "Recovery quote pin created successfully",
	})
}

// GetBoardPins retrieves pins from a specific board
func (h *PinterestHandler) GetBoardPins(c *gin.Context) {
	if h.pinterestService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Pinterest service not available"})
		return
	}

	boardID := c.Param("boardId")
	if boardID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Board ID is required"})
		return
	}

	limitStr := c.DefaultQuery("limit", "25")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 25
	}

	pins, err := h.pinterestService.GetBoardPins(boardID, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get board pins: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"pins":     pins,
		"count":    len(pins),
		"board_id": boardID,
		"status":   "success",
	})
}

// GetAnalytics retrieves Pinterest analytics
func (h *PinterestHandler) GetAnalytics(c *gin.Context) {
	if h.pinterestService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Pinterest service not available"})
		return
	}

	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		days = 30
	}

	analytics, err := h.pinterestService.GetAnalytics(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get analytics: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"analytics":   analytics,
		"period_days": days,
		"status":      "success",
	})
}

// ValidateToken checks if the Pinterest access token is valid
func (h *PinterestHandler) ValidateToken(c *gin.Context) {
	if h.pinterestService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Pinterest service not available"})
		return
	}

	err := h.pinterestService.ValidateToken()
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid": false,
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":   true,
		"message": "Pinterest token is valid",
	})
}

// SetupRecoveryBoards creates the standard recovery boards
func (h *PinterestHandler) SetupRecoveryBoards(c *gin.Context) {
	if h.pinterestService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Pinterest service not available"})
		return
	}

	recoveryBoards, err := h.pinterestService.GetOrCreateRecoveryBoards()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to setup recovery boards: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"boards":  recoveryBoards,
		"message": "Recovery boards setup completed",
	})
}
