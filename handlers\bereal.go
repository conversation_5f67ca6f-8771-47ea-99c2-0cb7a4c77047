package handlers

import (
	"net/http"
	"strconv"

	"recovery-dashboard/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// BeRealHandler handles BeReal-specific operations
type BeRealHandler struct {
	db            *gorm.DB
	berealService *services.BeRealService
}

// NewBeRealHandler creates a new BeReal handler
func NewBeRealHandler(db *gorm.DB, berealService *services.BeRealService) *BeRealHandler {
	return &BeRealHandler{
		db:            db,
		berealService: berealService,
	}
}

// GetUserInfo retrieves BeReal user information
func (h *BeRealHandler) GetUserInfo(c *gin.Context) {
	if h.berealService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "BeReal service not available"})
		return
	}

	userInfo, err := h.berealService.GetUserInfo()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info: " + err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"user":   userInfo,
		"status": "success",
	})
}

// GetCurrentMoment retrieves the current BeReal moment
func (h *BeRealHandler) GetCurrentMoment(c *gin.Context) {
	if h.berealService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "BeReal service not available"})
		return
	}

	moment, err := h.berealService.GetCurrentMoment()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get current moment: " + err.Error()})
		return
	}

	// Check if moment is active
	isActive, _, err := h.berealService.IsBeRealMomentActive()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check moment status: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"moment":    moment,
		"is_active": isActive,
		"status":    "success",
	})
}

// CreatePost creates a BeReal post
func (h *BeRealHandler) CreatePost(c *gin.Context) {
	if h.berealService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "BeReal service not available"})
		return
	}

	var req struct {
		Caption           string `json:"caption" binding:"required"`
		FrontPhotoPath    string `json:"front_photo_path"`
		BackPhotoPath     string `json:"back_photo_path"`
		Location          string `json:"location"`
		Visibility        string `json:"visibility"` // "friends", "public"
		IsRecoveryPost    bool   `json:"is_recovery_post"`
		RecoveryMilestone string `json:"recovery_milestone"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default visibility
	if req.Visibility == "" {
		req.Visibility = "friends"
	}

	postReq := services.BeRealCreatePostRequest{
		Caption:           req.Caption,
		FrontPhotoPath:    req.FrontPhotoPath,
		BackPhotoPath:     req.BackPhotoPath,
		Location:          req.Location,
		Visibility:        req.Visibility,
		IsRecoveryPost:    req.IsRecoveryPost,
		RecoveryMilestone: req.RecoveryMilestone,
	}

	postID, err := h.berealService.CreatePost(postReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create post: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"post_id": postID,
		"message": "BeReal post created successfully",
	})
}

// CreateRecoveryMoment creates an authentic recovery moment
func (h *BeRealHandler) CreateRecoveryMoment(c *gin.Context) {
	if h.berealService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "BeReal service not available"})
		return
	}

	var req struct {
		MomentType  string `json:"moment_type" binding:"required"` // daily_check_in, milestone, meeting, etc.
		Milestone   string `json:"milestone"`                      // e.g., "30 days", "1 year"
		Location    string `json:"location"`
		AccountType string `json:"account_type"` // "merch", "serious"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "serious"
	}

	// Validate moment type
	validTypes := []string{"daily_check_in", "milestone", "meeting", "gratitude", "challenge", "service", "nature", "workout"}
	validType := false
	for _, t := range validTypes {
		if t == req.MomentType {
			validType = true
			break
		}
	}

	if !validType {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":       "Invalid moment type",
			"valid_types": validTypes,
		})
		return
	}

	// Create the authentic recovery post
	postID, err := h.berealService.CreateAuthenticRecoveryPost(req.MomentType, req.Milestone, req.Location, req.AccountType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create recovery moment: " + err.Error()})
		return
	}

	// Generate preview of what was posted
	momentContent := h.berealService.GenerateRecoveryMoment(req.MomentType, req.Milestone, req.AccountType)

	c.JSON(http.StatusOK, gin.H{
		"success":           true,
		"post_id":           postID,
		"moment_type":       req.MomentType,
		"message":           "Authentic recovery moment created successfully",
		"generated_content": gin.H{
			"caption":            momentContent.Caption,
			"visibility":         momentContent.Visibility,
			"is_recovery_post":   momentContent.IsRecoveryPost,
			"recovery_milestone": momentContent.RecoveryMilestone,
		},
	})
}

// CreateMilestonePost creates a recovery milestone celebration post
func (h *BeRealHandler) CreateMilestonePost(c *gin.Context) {
	if h.berealService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "BeReal service not available"})
		return
	}

	var req struct {
		Milestone   string `json:"milestone" binding:"required"` // e.g., "30 days", "1 year"
		AccountType string `json:"account_type"`                 // "merch", "serious"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "serious"
	}

	// Validate milestone
	validMilestones := h.berealService.GetRecoveryMilestones()
	validMilestone := false
	for _, m := range validMilestones {
		if m == req.Milestone {
			validMilestone = true
			break
		}
	}

	if !validMilestone {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":            "Invalid milestone",
			"valid_milestones": validMilestones,
		})
		return
	}

	// Create the milestone post
	postID, err := h.berealService.CreateMilestonePost(req.Milestone, req.AccountType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create milestone post: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"post_id":   postID,
		"milestone": req.Milestone,
		"message":   "Recovery milestone post created successfully",
	})
}

// GetUserPosts retrieves user's BeReal posts
func (h *BeRealHandler) GetUserPosts(c *gin.Context) {
	if h.berealService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "BeReal service not available"})
		return
	}

	limitStr := c.DefaultQuery("limit", "25")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 || limit > 100 {
		limit = 25
	}

	posts, err := h.berealService.GetUserPosts(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get posts: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"posts":  posts,
		"count":  len(posts),
		"status": "success",
	})
}

// GetRecoveryMomentTypes returns types of recovery moments for BeReal
func (h *BeRealHandler) GetRecoveryMomentTypes(c *gin.Context) {
	if h.berealService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "BeReal service not available"})
		return
	}

	momentTypes := h.berealService.GetRecoveryMomentTypes()

	c.JSON(http.StatusOK, gin.H{
		"moment_types": momentTypes,
		"count":        len(momentTypes),
		"status":       "success",
	})
}

// GetRecoveryMilestones returns common recovery milestones
func (h *BeRealHandler) GetRecoveryMilestones(c *gin.Context) {
	if h.berealService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "BeReal service not available"})
		return
	}

	milestones := h.berealService.GetRecoveryMilestones()

	c.JSON(http.StatusOK, gin.H{
		"milestones": milestones,
		"count":      len(milestones),
		"status":     "success",
	})
}

// GetAnalytics retrieves BeReal analytics
func (h *BeRealHandler) GetAnalytics(c *gin.Context) {
	if h.berealService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "BeReal service not available"})
		return
	}

	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		days = 30
	}

	analytics, err := h.berealService.GetAnalytics(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get analytics: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"analytics":   analytics,
		"period_days": days,
		"status":      "success",
	})
}

// GetRecoveryAnalytics retrieves analytics specifically for recovery posts
func (h *BeRealHandler) GetRecoveryAnalytics(c *gin.Context) {
	if h.berealService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "BeReal service not available"})
		return
	}

	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		days = 30
	}

	analytics, err := h.berealService.GetRecoveryPostsAnalytics(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recovery analytics: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"recovery_analytics": analytics,
		"period_days":        days,
		"status":             "success",
	})
}

// ValidateToken checks if the BeReal access token is valid
func (h *BeRealHandler) ValidateToken(c *gin.Context) {
	if h.berealService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "BeReal service not available"})
		return
	}

	err := h.berealService.ValidateToken()
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid": false,
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":   true,
		"message": "BeReal token is valid",
	})
}
