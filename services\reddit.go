package services

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// RedditService handles Reddit API interactions
type RedditService struct {
	ClientID     string
	ClientSecret string
	Username     string
	Password     string
	UserAgent    string
	AccessToken  string
	BaseURL      string
}

// NewRedditService creates a new Reddit service instance
func NewRedditService(clientID, clientSecret, username, password, userAgent string) *RedditService {
	return &RedditService{
		ClientID:     clientID,
		ClientSecret: clientSecret,
		Username:     username,
		Password:     password,
		UserAgent:    userAgent,
		BaseURL:      "https://oauth.reddit.com",
	}
}

// RedditUser represents Reddit user information
type RedditUser struct {
	ID               string  `json:"id"`
	Name             string  `json:"name"`
	CommentKarma     int     `json:"comment_karma"`
	LinkKarma        int     `json:"link_karma"`
	TotalKarma       int     `json:"total_karma"`
	Created          float64 `json:"created"`
	CreatedUTC       float64 `json:"created_utc"`
	HasVerifiedEmail bool    `json:"has_verified_email"`
	IsEmployee       bool    `json:"is_employee"`
	IsMod            bool    `json:"is_mod"`
	IsPremium        bool    `json:"is_premium"`
	AcceptFollowers  bool    `json:"accept_followers"`
	HasSubscribed    bool    `json:"has_subscribed"`
}

// RedditSubreddit represents a Reddit subreddit
type RedditSubreddit struct {
	ID                      string  `json:"id"`
	Name                    string  `json:"display_name"`
	Title                   string  `json:"title"`
	Description             string  `json:"public_description"`
	Subscribers             int     `json:"subscribers"`
	ActiveUserCount         int     `json:"active_user_count"`
	Created                 float64 `json:"created"`
	CreatedUTC              float64 `json:"created_utc"`
	Over18                  bool    `json:"over18"`
	Lang                    string  `json:"lang"`
	URL                     string  `json:"url"`
	SubredditType           string  `json:"subreddit_type"`  // public, private, restricted
	SubmissionType          string  `json:"submission_type"` // any, link, self
	AllowImages             bool    `json:"allow_images"`
	AllowVideos             bool    `json:"allow_videos"`
	AllowPolls              bool    `json:"allow_polls"`
	CollapseDeletedComments bool    `json:"collapse_deleted_comments"`
}

// RedditPost represents a Reddit post/submission
type RedditPost struct {
	ID          string  `json:"id"`
	Title       string  `json:"title"`
	Selftext    string  `json:"selftext"`
	URL         string  `json:"url"`
	Permalink   string  `json:"permalink"`
	Subreddit   string  `json:"subreddit"`
	SubredditID string  `json:"subreddit_id"`
	Author      string  `json:"author"`
	Score       int     `json:"score"`
	UpvoteRatio float64 `json:"upvote_ratio"`
	NumComments int     `json:"num_comments"`
	Created     float64 `json:"created"`
	CreatedUTC  float64 `json:"created_utc"`
	Edited      bool    `json:"edited"`
	Locked      bool    `json:"locked"`
	Pinned      bool    `json:"pinned"`
	Stickied    bool    `json:"stickied"`
	NSFW        bool    `json:"over_18"`
	IsSelf      bool    `json:"is_self"`
	Domain      string  `json:"domain"`
	PostHint    string  `json:"post_hint"`
	Thumbnail   string  `json:"thumbnail"`
}

// RedditComment represents a Reddit comment
type RedditComment struct {
	ID               string  `json:"id"`
	Body             string  `json:"body"`
	BodyHTML         string  `json:"body_html"`
	Author           string  `json:"author"`
	Score            int     `json:"score"`
	Created          float64 `json:"created"`
	CreatedUTC       float64 `json:"created_utc"`
	Edited           bool    `json:"edited"`
	ParentID         string  `json:"parent_id"`
	LinkID           string  `json:"link_id"`
	SubredditID      string  `json:"subreddit_id"`
	Subreddit        string  `json:"subreddit"`
	Permalink        string  `json:"permalink"`
	Depth            int     `json:"depth"`
	IsSubmitter      bool    `json:"is_submitter"`
	ScoreHidden      bool    `json:"score_hidden"`
	Controversiality int     `json:"controversiality"`
}

// RedditAnalytics represents Reddit analytics data
type RedditAnalytics struct {
	TotalPosts       int                    `json:"total_posts"`
	TotalComments    int                    `json:"total_comments"`
	TotalKarma       int                    `json:"total_karma"`
	CommentKarma     int                    `json:"comment_karma"`
	LinkKarma        int                    `json:"link_karma"`
	AverageScore     float64                `json:"average_score"`
	TopSubreddits    []string               `json:"top_subreddits"`
	EngagementRate   float64                `json:"engagement_rate"`
	CommunityGrowth  float64                `json:"community_growth"`
	SubredditMetrics map[string]interface{} `json:"subreddit_metrics"`
}

// RedditSubmitRequest represents a request to submit a post
type RedditSubmitRequest struct {
	Subreddit   string `json:"sr"`
	Title       string `json:"title"`
	Text        string `json:"text,omitempty"`
	URL         string `json:"url,omitempty"`
	Kind        string `json:"kind"` // "self" for text posts, "link" for URL posts
	Resubmit    bool   `json:"resubmit"`
	SendReplies bool   `json:"sendreplies"`
}

// RedditCommentRequest represents a request to comment
type RedditCommentRequest struct {
	ThingID string `json:"thing_id"` // fullname of the thing being replied to
	Text    string `json:"text"`
}

// Authenticate gets an access token for Reddit API
func (rs *RedditService) Authenticate() error {
	authURL := "https://www.reddit.com/api/v1/access_token"

	data := url.Values{}
	data.Set("grant_type", "password")
	data.Set("username", rs.Username)
	data.Set("password", rs.Password)

	req, err := http.NewRequest("POST", authURL, strings.NewReader(data.Encode()))
	if err != nil {
		return fmt.Errorf("failed to create auth request: %w", err)
	}

	req.SetBasicAuth(rs.ClientID, rs.ClientSecret)
	req.Header.Set("User-Agent", rs.UserAgent)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to authenticate: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("Reddit auth error: %s", string(body))
	}

	var authResponse struct {
		AccessToken string `json:"access_token"`
		TokenType   string `json:"token_type"`
		ExpiresIn   int    `json:"expires_in"`
		Scope       string `json:"scope"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&authResponse); err != nil {
		return fmt.Errorf("failed to decode auth response: %w", err)
	}

	rs.AccessToken = authResponse.AccessToken
	return nil
}

// GetUserInfo retrieves Reddit user information
func (rs *RedditService) GetUserInfo() (*RedditUser, error) {
	if rs.AccessToken == "" {
		if err := rs.Authenticate(); err != nil {
			return nil, err
		}
	}

	url := fmt.Sprintf("%s/api/v1/me", rs.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+rs.AccessToken)
	req.Header.Set("User-Agent", rs.UserAgent)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Reddit API error: %s", string(body))
	}

	var user RedditUser
	if err := json.NewDecoder(resp.Body).Decode(&user); err != nil {
		return nil, fmt.Errorf("failed to decode user info: %w", err)
	}

	return &user, nil
}

// GetSubredditInfo retrieves information about a specific subreddit
func (rs *RedditService) GetSubredditInfo(subredditName string) (*RedditSubreddit, error) {
	if rs.AccessToken == "" {
		if err := rs.Authenticate(); err != nil {
			return nil, err
		}
	}

	url := fmt.Sprintf("%s/r/%s/about", rs.BaseURL, subredditName)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+rs.AccessToken)
	req.Header.Set("User-Agent", rs.UserAgent)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get subreddit info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Reddit API error: %s", string(body))
	}

	var response struct {
		Kind string          `json:"kind"`
		Data RedditSubreddit `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode subreddit info: %w", err)
	}

	return &response.Data, nil
}

// SubmitPost submits a new post to a subreddit
func (rs *RedditService) SubmitPost(req RedditSubmitRequest) (string, error) {
	if rs.AccessToken == "" {
		if err := rs.Authenticate(); err != nil {
			return "", err
		}
	}

	submitURL := fmt.Sprintf("%s/api/submit", rs.BaseURL)

	// Convert struct to form data
	data := url.Values{}
	data.Set("sr", req.Subreddit)
	data.Set("title", req.Title)
	data.Set("kind", req.Kind)
	data.Set("resubmit", fmt.Sprintf("%t", req.Resubmit))
	data.Set("sendreplies", fmt.Sprintf("%t", req.SendReplies))

	if req.Kind == "self" {
		data.Set("text", req.Text)
	} else {
		data.Set("url", req.URL)
	}

	httpReq, err := http.NewRequest("POST", submitURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+rs.AccessToken)
	httpReq.Header.Set("User-Agent", rs.UserAgent)
	httpReq.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to submit post: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Reddit API error: %s", string(body))
	}

	var response struct {
		JSON struct {
			Errors [][]string `json:"errors"`
			Data   struct {
				ID   string `json:"id"`
				Name string `json:"name"`
				URL  string `json:"url"`
			} `json:"data"`
		} `json:"json"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	if len(response.JSON.Errors) > 0 {
		return "", fmt.Errorf("Reddit submission error: %v", response.JSON.Errors)
	}

	return response.JSON.Data.ID, nil
}

// GetAnalytics retrieves Reddit analytics (mock implementation)
func (rs *RedditService) GetAnalytics(days int) (*RedditAnalytics, error) {
	// Mock analytics for now - in production, this would aggregate user's posts and comments
	analytics := &RedditAnalytics{
		TotalPosts:      45,
		TotalComments:   156,
		TotalKarma:      2340,
		CommentKarma:    1890,
		LinkKarma:       450,
		AverageScore:    15.2,
		TopSubreddits:   []string{"recovery", "stopdrinking", "alcoholicsanonymous", "REDDITORSINRECOVERY"},
		EngagementRate:  8.5,
		CommunityGrowth: 12.3,
		SubredditMetrics: map[string]interface{}{
			"recovery": map[string]interface{}{
				"posts":    12,
				"comments": 45,
				"karma":    567,
			},
			"stopdrinking": map[string]interface{}{
				"posts":    8,
				"comments": 34,
				"karma":    423,
			},
		},
	}

	return analytics, nil
}

// ValidateToken checks if the Reddit credentials are valid
func (rs *RedditService) ValidateToken() error {
	_, err := rs.GetUserInfo()
	return err
}

// GenerateRecoveryPost creates Reddit-appropriate content from YouTube videos
func (rs *RedditService) GenerateRecoveryPost(videoTitle, videoURL, subreddit, accountType string) RedditSubmitRequest {
	var title, text string

	// Subreddit-specific content adaptation
	switch subreddit {
	case "recovery", "REDDITORSINRECOVERY":
		title = fmt.Sprintf("Sharing: %s", videoTitle)
		text = fmt.Sprintf("Found this recovery content that really resonated with me:\n\n%s\n\nWhat are your thoughts on this message? How does it relate to your own recovery journey?\n\n[Video Link](%s)\n\n*Sharing in the spirit of recovery and community support*", videoTitle, videoURL)

	case "stopdrinking":
		title = fmt.Sprintf("Recovery Wisdom: %s", videoTitle)
		text = fmt.Sprintf("This recovery content helped me today:\n\n%s\n\nFor anyone who might benefit from this message. We're all in this together.\n\n[Watch Here](%s)\n\n*IWNDWYT (I Will Not Drink With You Today)*", videoTitle, videoURL)

	case "alcoholicsanonymous":
		title = fmt.Sprintf("Speaker Share: %s", videoTitle)
		text = fmt.Sprintf("Powerful AA speaker content:\n\n%s\n\nThis message speaks to the heart of our program. Hope it helps someone today.\n\n[Listen Here](%s)\n\n*Keep coming back*", videoTitle, videoURL)

	case "addiction":
		title = fmt.Sprintf("Recovery Resource: %s", videoTitle)
		text = fmt.Sprintf("Sharing this recovery resource:\n\n%s\n\nRecovery is possible. If you're struggling, please know you're not alone.\n\n[Resource Link](%s)\n\n*One day at a time*", videoTitle, videoURL)

	default:
		title = fmt.Sprintf("Recovery Content: %s", videoTitle)
		text = fmt.Sprintf("Sharing some recovery wisdom:\n\n%s\n\n[Link](%s)", videoTitle, videoURL)
	}

	// Account type modifications
	switch accountType {
	case "merch":
		text += "\n\n*Also check out recovery merchandise and resources at our community store*"
	case "serious":
		// Keep it purely focused on recovery message
		break
	}

	return RedditSubmitRequest{
		Subreddit:   subreddit,
		Title:       title,
		Text:        text,
		Kind:        "self", // Text post
		Resubmit:    false,
		SendReplies: true,
	}
}

// CrossPostYouTubeVideo creates a Reddit post for a YouTube video
func (rs *RedditService) CrossPostYouTubeVideo(videoTitle, videoURL, subreddit, accountType string) (string, error) {
	postContent := rs.GenerateRecoveryPost(videoTitle, videoURL, subreddit, accountType)
	return rs.SubmitPost(postContent)
}

// GetRecoverySubreddits returns a list of recovery-focused subreddits
func (rs *RedditService) GetRecoverySubreddits() []struct {
	Name        string
	Description string
	Focus       string
	Guidelines  string
} {
	return []struct {
		Name        string
		Description string
		Focus       string
		Guidelines  string
	}{
		{
			Name:        "recovery",
			Description: "A community for those in recovery from any addiction",
			Focus:       "General recovery support and discussion",
			Guidelines:  "Be supportive, no promotion of substances, respect anonymity",
		},
		{
			Name:        "stopdrinking",
			Description: "This subreddit is a place to motivate each other to control or stop drinking",
			Focus:       "Alcohol recovery and sobriety support",
			Guidelines:  "No promotion of drinking, be supportive, use IWNDWYT",
		},
		{
			Name:        "alcoholicsanonymous",
			Description: "A community for members and friends of Alcoholics Anonymous",
			Focus:       "AA program discussion and support",
			Guidelines:  "Follow AA traditions, respect anonymity, no outside issues",
		},
		{
			Name:        "REDDITORSINRECOVERY",
			Description: "Reddit community for people in recovery from addiction",
			Focus:       "Recovery community building and support",
			Guidelines:  "Be respectful, no substance promotion, support recovery",
		},
		{
			Name:        "addiction",
			Description: "A place to discuss addiction and recovery",
			Focus:       "Addiction education and recovery resources",
			Guidelines:  "Educational focus, no substance promotion, be supportive",
		},
		{
			Name:        "NarcoticsAnonymous",
			Description: "A community for members and friends of Narcotics Anonymous",
			Focus:       "NA program discussion and support",
			Guidelines:  "Follow NA traditions, respect anonymity, focus on recovery",
		},
		{
			Name:        "OpiatesRecovery",
			Description: "A community for those recovering from opiate addiction",
			Focus:       "Opiate-specific recovery support",
			Guidelines:  "No substance discussion, recovery focus only, be supportive",
		},
		{
			Name:        "leaves",
			Description: "A supportive community for those who want to quit cannabis",
			Focus:       "Cannabis cessation and recovery",
			Guidelines:  "No substance promotion, be supportive, focus on quitting",
		},
	}
}

// CreateRecoveryDiscussion creates a discussion post about recovery topics
func (rs *RedditService) CreateRecoveryDiscussion(topic, subreddit, accountType string) (string, error) {
	var title, text string

	switch topic {
	case "daily_reflection":
		title = "Daily Recovery Reflection - What's helping you today?"
		text = "Taking a moment to reflect on our recovery journey today.\n\nWhat's one thing that's helping you stay strong in your recovery right now? It could be a meeting, a conversation, a book, a practice, or just a simple realization.\n\nLet's support each other by sharing what's working. 🙏\n\n*One day at a time*"

	case "gratitude":
		title = "Gratitude in Recovery - What are you grateful for today?"
		text = "Gratitude is such a powerful tool in recovery. It helps shift our perspective and reminds us of the good in our lives.\n\nWhat's something you're grateful for in your recovery journey today? Big or small, everything counts.\n\nLet's spread some positivity and gratitude! ✨"

	case "challenges":
		title = "Recovery Challenges - How do you handle difficult days?"
		text = "We all face challenges in recovery. Some days are harder than others, and that's completely normal.\n\nWhat strategies do you use when you're having a tough day? How do you get through the difficult moments?\n\nSharing our experiences can help others who might be struggling. 💪"

	case "milestones":
		title = "Celebrating Recovery Milestones - Share your victories!"
		text = "Every milestone in recovery deserves to be celebrated, whether it's one day, one week, one month, or many years.\n\nWhat recovery milestone are you celebrating or working toward? Let's celebrate together!\n\nRemember: Progress, not perfection. Every day counts. 🎉"

	default:
		title = "Recovery Discussion - Let's talk about our journey"
		text = "Opening up a space for recovery discussion and mutual support.\n\nHow is everyone doing in their recovery today? What's on your mind?\n\nThis is a safe space to share, ask questions, or just check in with the community. 💙"
	}

	postReq := RedditSubmitRequest{
		Subreddit:   subreddit,
		Title:       title,
		Text:        text,
		Kind:        "self",
		Resubmit:    false,
		SendReplies: true,
	}

	return rs.SubmitPost(postReq)
}

// GetSubredditGuidelines returns posting guidelines for recovery subreddits
func (rs *RedditService) GetSubredditGuidelines(subreddit string) map[string]interface{} {
	guidelines := map[string]map[string]interface{}{
		"recovery": {
			"posting_rules": []string{
				"Be supportive and respectful",
				"No promotion of substances or relapse",
				"Respect anonymity and privacy",
				"Stay on topic - recovery focused",
				"No spam or self-promotion without value",
			},
			"content_tips": []string{
				"Share personal experiences authentically",
				"Ask thoughtful questions",
				"Provide support and encouragement",
				"Share resources that helped you",
			},
			"engagement_style": "Supportive, authentic, community-focused",
		},
		"stopdrinking": {
			"posting_rules": []string{
				"No promotion of drinking",
				"Be supportive of sobriety goals",
				"Use IWNDWYT (I Will Not Drink With You Today)",
				"Respect different paths to sobriety",
				"No medical advice",
			},
			"content_tips": []string{
				"Share your sober journey",
				"Celebrate milestones",
				"Ask for support when needed",
				"Share what's working for you",
			},
			"engagement_style": "Encouraging, milestone-focused, daily support",
		},
		"alcoholicsanonymous": {
			"posting_rules": []string{
				"Follow AA traditions",
				"Respect anonymity at all levels",
				"No outside issues or controversies",
				"Focus on AA program and recovery",
				"No promotion of other programs",
			},
			"content_tips": []string{
				"Share experience, strength, and hope",
				"Discuss AA literature and concepts",
				"Ask program-related questions",
				"Share speaker content appropriately",
			},
			"engagement_style": "Program-focused, traditional, respectful",
		},
	}

	if guide, exists := guidelines[subreddit]; exists {
		return guide
	}

	// Default guidelines
	return map[string]interface{}{
		"posting_rules": []string{
			"Be respectful and supportive",
			"Stay on topic",
			"No spam or excessive self-promotion",
			"Follow community guidelines",
		},
		"content_tips": []string{
			"Share authentic experiences",
			"Be helpful to others",
			"Ask thoughtful questions",
			"Engage meaningfully",
		},
		"engagement_style": "Respectful, helpful, community-minded",
	}
}
