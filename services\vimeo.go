package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// VimeoService handles Vimeo API interactions
type VimeoService struct {
	AccessToken string
	BaseURL     string
	UserID      string
}

// NewVimeoService creates a new Vimeo service instance
func NewVimeoService(accessToken, userID string) *VimeoService {
	return &VimeoService{
		AccessToken: accessToken,
		BaseURL:     "https://api.vimeo.com",
		UserID:      userID,
	}
}

// VimeoUser represents Vimeo user information
type VimeoUser struct {
	URI                 string           `json:"uri"`
	Name                string           `json:"name"`
	Link                string           `json:"link"`
	Location            string           `json:"location"`
	Bio                 string           `json:"bio"`
	ShortBio            string           `json:"short_bio"`
	CreatedTime         time.Time        `json:"created_time"`
	PicturesURI         string           `json:"pictures_uri"`
	Account             string           `json:"account"` // basic, plus, pro, business, premium
	ContentFilter       []string         `json:"content_filter"`
	UploadQuota         VimeoQuota       `json:"upload_quota"`
	MetadataConnections VimeoConnections `json:"metadata_connections"`
}

// VimeoQuota represents upload quota information
type VimeoQuota struct {
	Space VimeoSpace `json:"space"`
	Quota VimeoSpace `json:"quota"`
}

// VimeoSpace represents storage space information
type VimeoSpace struct {
	Free    int64  `json:"free"`
	Max     int64  `json:"max"`
	Used    int64  `json:"used"`
	Showing string `json:"showing"`
}

// VimeoConnections represents user connections
type VimeoConnections struct {
	Albums    VimeoConnection `json:"albums"`
	Videos    VimeoConnection `json:"videos"`
	Followers VimeoConnection `json:"followers"`
	Following VimeoConnection `json:"following"`
}

// VimeoConnection represents connection information
type VimeoConnection struct {
	URI   string `json:"uri"`
	Total int    `json:"total"`
}

// VimeoVideo represents a Vimeo video
type VimeoVideo struct {
	URI           string          `json:"uri"`
	Name          string          `json:"name"`
	Description   string          `json:"description"`
	Type          string          `json:"type"`
	Link          string          `json:"link"`
	Duration      int             `json:"duration"`
	Width         int             `json:"width"`
	Height        int             `json:"height"`
	Language      string          `json:"language"`
	CreatedTime   time.Time       `json:"created_time"`
	ModifiedTime  time.Time       `json:"modified_time"`
	ReleaseTime   time.Time       `json:"release_time"`
	ContentRating []string        `json:"content_rating"`
	License       string          `json:"license"`
	Privacy       VimeoPrivacy    `json:"privacy"`
	Pictures      VimeoPictures   `json:"pictures"`
	Tags          []VimeoTag      `json:"tags"`
	Stats         VimeoStats      `json:"stats"`
	Categories    []VimeoCategory `json:"categories"`
	Status        string          `json:"status"` // available, quota_exceeded, total_cap_exceeded, etc.
	ResourceKey   string          `json:"resource_key"`
	EmbedHTML     string          `json:"embed_html"`
}

// VimeoPrivacy represents video privacy settings
type VimeoPrivacy struct {
	View     string `json:"view"`  // anybody, nobody, contacts, password, users, disable
	Embed    string `json:"embed"` // public, private
	Download bool   `json:"download"`
	Add      bool   `json:"add"`
	Comments string `json:"comments"` // anybody, contacts, nobody
}

// VimeoPictures represents video thumbnails
type VimeoPictures struct {
	URI    string             `json:"uri"`
	Active bool               `json:"active"`
	Type   string             `json:"type"`
	Sizes  []VimeoPictureSize `json:"sizes"`
}

// VimeoPictureSize represents thumbnail size
type VimeoPictureSize struct {
	Width  int    `json:"width"`
	Height int    `json:"height"`
	Link   string `json:"link"`
}

// VimeoTag represents video tag
type VimeoTag struct {
	URI       string `json:"uri"`
	Name      string `json:"name"`
	Tag       string `json:"tag"`
	Canonical string `json:"canonical"`
}

// VimeoStats represents video statistics
type VimeoStats struct {
	Plays int `json:"plays"`
}

// VimeoCategory represents video category
type VimeoCategory struct {
	URI                   string                `json:"uri"`
	Name                  string                `json:"name"`
	Link                  string                `json:"link"`
	TopLevel              bool                  `json:"top_level"`
	Pictures              VimeoPictures         `json:"pictures"`
	LastVideoFeaturedTime time.Time             `json:"last_video_featured_time"`
	Parent                *VimeoCategory        `json:"parent"`
	Metadata              VimeoCategoryMetadata `json:"metadata"`
	Subcategories         []VimeoCategory       `json:"subcategories"`
}

// VimeoCategoryMetadata represents category metadata
type VimeoCategoryMetadata struct {
	Connections VimeoCategoryConnections `json:"connections"`
}

// VimeoCategoryConnections represents category connections
type VimeoCategoryConnections struct {
	Channels VimeoConnection `json:"channels"`
	Groups   VimeoConnection `json:"groups"`
	Users    VimeoConnection `json:"users"`
	Videos   VimeoConnection `json:"videos"`
}

// VimeoUploadRequest represents a video upload request
type VimeoUploadRequest struct {
	Name          string             `json:"name"`
	Description   string             `json:"description"`
	Privacy       VimeoPrivacy       `json:"privacy"`
	Password      string             `json:"password,omitempty"`
	ReviewPage    VimeoReviewPage    `json:"review_page"`
	ContentRating []string           `json:"content_rating,omitempty"`
	Embed         VimeoEmbedSettings `json:"embed"`
	SpeakerID     uint               `json:"speaker_id,omitempty"`
	Tags          []string           `json:"tags,omitempty"`
}

// VimeoReviewPage represents review page settings
type VimeoReviewPage struct {
	Active bool `json:"active"`
}

// VimeoEmbedSettings represents embed settings
type VimeoEmbedSettings struct {
	Buttons VimeoEmbedButtons `json:"buttons"`
	Logos   VimeoEmbedLogos   `json:"logos"`
	Title   VimeoEmbedTitle   `json:"title"`
	Playbar bool              `json:"playbar"`
	Volume  bool              `json:"volume"`
	Speed   bool              `json:"speed"`
	Color   string            `json:"color"`
}

// VimeoEmbedButtons represents embed button settings
type VimeoEmbedButtons struct {
	Like       bool `json:"like"`
	Watchlater bool `json:"watchlater"`
	Share      bool `json:"share"`
	Embed      bool `json:"embed"`
	HD         bool `json:"hd"`
	Fullscreen bool `json:"fullscreen"`
	Scaling    bool `json:"scaling"`
}

// VimeoEmbedLogos represents embed logo settings
type VimeoEmbedLogos struct {
	Vimeo  bool            `json:"vimeo"`
	Custom VimeoCustomLogo `json:"custom"`
}

// VimeoCustomLogo represents custom logo settings
type VimeoCustomLogo struct {
	Active bool   `json:"active"`
	Link   string `json:"link"`
	Sticky bool   `json:"sticky"`
}

// VimeoEmbedTitle represents embed title settings
type VimeoEmbedTitle struct {
	Name     string `json:"name"`
	Owner    string `json:"owner"`
	Portrait string `json:"portrait"`
}

// GetUser retrieves Vimeo user information
func (vs *VimeoService) GetUser() (*VimeoUser, error) {
	url := fmt.Sprintf("%s/me", vs.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+vs.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Vimeo API error: %s", string(body))
	}

	var user VimeoUser
	if err := json.NewDecoder(resp.Body).Decode(&user); err != nil {
		return nil, fmt.Errorf("failed to decode user: %w", err)
	}

	return &user, nil
}

// GetUserVideos retrieves user's videos
func (vs *VimeoService) GetUserVideos(page, perPage int) ([]VimeoVideo, error) {
	if page <= 0 {
		page = 1
	}
	if perPage <= 0 || perPage > 100 {
		perPage = 25
	}

	url := fmt.Sprintf("%s/me/videos?page=%d&per_page=%d", vs.BaseURL, page, perPage)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+vs.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get videos: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Vimeo API error: %s", string(body))
	}

	var response struct {
		Total   int          `json:"total"`
		Page    int          `json:"page"`
		PerPage int          `json:"per_page"`
		Paging  interface{}  `json:"paging"`
		Data    []VimeoVideo `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode videos: %w", err)
	}

	return response.Data, nil
}

// ValidateToken checks if the Vimeo access token is valid
func (vs *VimeoService) ValidateToken() error {
	_, err := vs.GetUser()
	return err
}

// CreateRecoveryVideo creates a recovery-focused video upload request
func (vs *VimeoService) CreateRecoveryVideo(title, description, speakerName, program, topic string, speakerID uint, isPrivate bool) VimeoUploadRequest {
	// Generate recovery-specific tags
	tags := []string{
		"recovery",
		"sobriety",
		"addiction recovery",
		"sound of recovery",
		program,
		"recovery speaker",
		"hope",
		"healing",
		"transformation",
	}

	if program == "AA" {
		tags = append(tags, "alcoholics anonymous", "alcohol recovery")
	} else if program == "NA" {
		tags = append(tags, "narcotics anonymous", "drug recovery")
	}

	if topic != "" {
		tags = append(tags, topic)
	}

	// Set privacy based on content sensitivity
	privacy := VimeoPrivacy{
		View:     "anybody",
		Embed:    "public",
		Download: false,
		Add:      false,
		Comments: "contacts",
	}

	if isPrivate {
		privacy.View = "contacts"
		privacy.Embed = "private"
	}

	// Professional embed settings for recovery content
	embed := VimeoEmbedSettings{
		Buttons: VimeoEmbedButtons{
			Like:       true,
			Watchlater: true,
			Share:      true,
			Embed:      true,
			HD:         true,
			Fullscreen: true,
			Scaling:    true,
		},
		Logos: VimeoEmbedLogos{
			Vimeo: true,
			Custom: VimeoCustomLogo{
				Active: false, // Could be enabled for branding
			},
		},
		Title: VimeoEmbedTitle{
			Name:     "show",
			Owner:    "show",
			Portrait: "show",
		},
		Playbar: true,
		Volume:  true,
		Speed:   true,
		Color:   "00adef", // Recovery blue color
	}

	return VimeoUploadRequest{
		Name:        title,
		Description: description,
		Privacy:     privacy,
		ReviewPage: VimeoReviewPage{
			Active: false, // Skip review for automated uploads
		},
		ContentRating: []string{"safe"}, // Recovery content is safe for all audiences
		Embed:         embed,
		SpeakerID:     speakerID,
		Tags:          tags,
	}
}

// UploadRecoveryVideo uploads a recovery speaker video to Vimeo
func (vs *VimeoService) UploadRecoveryVideo(videoPath, title, description, speakerName, program, topic string, speakerID uint, isPrivate bool) (string, error) {
	// Create upload request
	uploadReq := vs.CreateRecoveryVideo(title, description, speakerName, program, topic, speakerID, isPrivate)

	// Step 1: Create the video entry
	createURL := fmt.Sprintf("%s/me/videos", vs.BaseURL)

	jsonBody, err := json.Marshal(uploadReq)
	if err != nil {
		return "", fmt.Errorf("failed to marshal upload request: %w", err)
	}

	req, err := http.NewRequest("POST", createURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+vs.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to create video: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Vimeo API error: %s", string(body))
	}

	var createResponse struct {
		URI    string `json:"uri"`
		Link   string `json:"link"`
		Upload struct {
			UploadLink string `json:"upload_link"`
			Form       string `json:"form"`
		} `json:"upload"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&createResponse); err != nil {
		return "", fmt.Errorf("failed to decode create response: %w", err)
	}

	// Extract video ID from URI (format: /videos/123456789)
	videoID := createResponse.URI[8:] // Remove "/videos/" prefix

	// Step 2: Upload the actual video file would happen here
	// This is a simplified implementation - in production, you'd upload the file
	// using the upload_link provided in the response

	return videoID, nil
}

// GetRecoveryVideoAnalytics retrieves analytics for recovery videos
func (vs *VimeoService) GetRecoveryVideoAnalytics(days int) (map[string]interface{}, error) {
	// Get user's videos
	videos, err := vs.GetUserVideos(1, 100)
	if err != nil {
		return nil, err
	}

	// Filter recovery videos from the specified time period
	cutoffTime := time.Now().AddDate(0, 0, -days)
	var recoveryVideos []VimeoVideo
	var totalPlays int

	for _, video := range videos {
		if video.CreatedTime.After(cutoffTime) {
			// Check if it's a recovery video by tags or description
			isRecoveryVideo := false
			for _, tag := range video.Tags {
				if tag.Tag == "recovery" || tag.Tag == "sobriety" {
					isRecoveryVideo = true
					break
				}
			}

			if isRecoveryVideo {
				recoveryVideos = append(recoveryVideos, video)
				totalPlays += video.Stats.Plays
			}
		}
	}

	// Calculate analytics
	avgPlays := float64(0)
	if len(recoveryVideos) > 0 {
		avgPlays = float64(totalPlays) / float64(len(recoveryVideos))
	}

	analytics := map[string]interface{}{
		"total_recovery_videos": len(recoveryVideos),
		"total_plays":           totalPlays,
		"average_plays":         avgPlays,
		"period_days":           days,
		"platform":              "vimeo",
		"video_quality":         "HD",       // Vimeo's strength
		"privacy_controls":      "Advanced", // Vimeo's privacy features
		"professional_hosting":  true,
	}

	return analytics, nil
}

// GetRecoveryCategories returns Vimeo categories suitable for recovery content
func (vs *VimeoService) GetRecoveryCategories() []string {
	return []string{
		"Documentary",
		"Education",
		"Health & Wellness",
		"Inspirational",
		"Personal Stories",
		"Self-Help",
		"Spirituality",
		"Support Groups",
	}
}

// CrossPostFromYouTube creates a Vimeo version of a YouTube video
func (vs *VimeoService) CrossPostFromYouTube(youtubeVideoID, youtubeTitle, youtubeDescription string, speakerID uint) (string, error) {
	// Adapt YouTube content for Vimeo's professional audience
	vimeoTitle := fmt.Sprintf("Recovery Speaker: %s", youtubeTitle)
	vimeoDescription := fmt.Sprintf("%s\n\nThis recovery content is also available on our YouTube channel.\n\nFor more recovery resources, visit our website.\n\n#Recovery #Sobriety #Hope #Healing", youtubeDescription)

	// Create professional Vimeo upload request
	_ = vs.CreateRecoveryVideo(vimeoTitle, vimeoDescription, "", "AA", "", speakerID, false)

	// In production, this would:
	// 1. Download the video from YouTube (if permitted)
	// 2. Upload to Vimeo with professional settings
	// 3. Return the Vimeo video ID

	// For now, return a mock video ID
	return "vimeo_" + youtubeVideoID, nil
}
