package services

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"recovery-dashboard/config"
	"recovery-dashboard/models"

	"google.golang.org/api/option"
	"google.golang.org/api/youtube/v3"
	"gorm.io/gorm"
)

type YouTubeService struct {
	service   *youtube.Service
	channelID string
	db        *gorm.DB
	cfg       *config.Config
}

type UploadRequest struct {
	VideoPath   string
	Title       string
	Description string
	Tags        []string
	CategoryID  string
	Privacy     string // "private", "unlisted", "public"
	SpeakerID   uint
}

type VideoAnalytics struct {
	VideoID      string    `json:"video_id"`
	Title        string    `json:"title"`
	Views        int64     `json:"views"`
	Likes        int64     `json:"likes"`
	Comments     int64     `json:"comments"`
	Duration     string    `json:"duration"`
	PublishedAt  time.Time `json:"published_at"`
	ThumbnailURL string    `json:"thumbnail_url"`
}

func NewYouTubeService(cfg *config.Config, db *gorm.DB) (*YouTubeService, error) {
	ctx := context.Background()

	// Initialize YouTube service with API key
	service, err := youtube.NewService(ctx, option.WithAPIKey(cfg.YouTubeAPIKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create YouTube service: %w", err)
	}

	return &YouTubeService{
		service:   service,
		channelID: cfg.YouTubeChannelID,
		db:        db,
		cfg:       cfg,
	}, nil
}

// UploadVideo uploads a video to YouTube and updates the database
func (ys *YouTubeService) UploadVideo(ctx context.Context, req UploadRequest) (*youtube.Video, error) {
	// Open video file
	file, err := os.Open(req.VideoPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open video file: %w", err)
	}
	defer file.Close()

	// Create video snippet
	video := &youtube.Video{
		Snippet: &youtube.VideoSnippet{
			Title:       req.Title,
			Description: req.Description,
			Tags:        req.Tags,
			CategoryId:  req.CategoryID,
			ChannelId:   ys.channelID,
		},
		Status: &youtube.VideoStatus{
			PrivacyStatus: req.Privacy,
		},
	}

	// Create upload call
	call := ys.service.Videos.Insert([]string{"snippet", "status"}, video)

	// Set the media upload
	call = call.Media(file)

	// Execute upload
	response, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to upload video: %w", err)
	}

	// Update database if speaker ID provided
	if req.SpeakerID > 0 && ys.db != nil {
		videoRecord := models.Video{
			SpeakerID:   req.SpeakerID,
			Title:       req.Title,
			RenderState: "uploaded",
			URL:         fmt.Sprintf("https://www.youtube.com/watch?v=%s", response.Id),
		}

		if err := ys.db.Create(&videoRecord).Error; err != nil {
			log.Printf("Failed to save video to database: %v", err)
		}
	}

	log.Printf("Video uploaded successfully: %s (ID: %s)", req.Title, response.Id)
	return response, nil
}

// GetChannelVideos retrieves videos from the Sound of Recovery channel
func (ys *YouTubeService) GetChannelVideos(ctx context.Context, maxResults int64) ([]*youtube.SearchResult, error) {
	call := ys.service.Search.List([]string{"snippet"}).
		ChannelId(ys.channelID).
		Type("video").
		Order("date").
		MaxResults(maxResults)

	response, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to get channel videos: %w", err)
	}

	return response.Items, nil
}

// GetVideoAnalytics retrieves analytics for a specific video
func (ys *YouTubeService) GetVideoAnalytics(ctx context.Context, videoID string) (*VideoAnalytics, error) {
	// Get video details
	call := ys.service.Videos.List([]string{"snippet", "statistics", "contentDetails"}).
		Id(videoID)

	response, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to get video details: %w", err)
	}

	if len(response.Items) == 0 {
		return nil, fmt.Errorf("video not found: %s", videoID)
	}

	video := response.Items[0]

	// Parse published date
	publishedAt, _ := time.Parse(time.RFC3339, video.Snippet.PublishedAt)

	analytics := &VideoAnalytics{
		VideoID:      videoID,
		Title:        video.Snippet.Title,
		Views:        int64(video.Statistics.ViewCount),
		Likes:        int64(video.Statistics.LikeCount),
		Comments:     int64(video.Statistics.CommentCount),
		Duration:     video.ContentDetails.Duration,
		PublishedAt:  publishedAt,
		ThumbnailURL: video.Snippet.Thumbnails.Medium.Url,
	}

	return analytics, nil
}

// GetChannelAnalytics retrieves overall channel analytics
func (ys *YouTubeService) GetChannelAnalytics(ctx context.Context) (map[string]interface{}, error) {
	// Get channel details
	call := ys.service.Channels.List([]string{"snippet", "statistics"}).
		Id(ys.channelID)

	response, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to get channel details: %w", err)
	}

	if len(response.Items) == 0 {
		return nil, fmt.Errorf("channel not found: %s", ys.channelID)
	}

	channel := response.Items[0]

	analytics := map[string]interface{}{
		"channel_id":       ys.channelID,
		"channel_title":    channel.Snippet.Title,
		"subscriber_count": channel.Statistics.SubscriberCount,
		"video_count":      channel.Statistics.VideoCount,
		"view_count":       channel.Statistics.ViewCount,
		"description":      channel.Snippet.Description,
		"published_at":     channel.Snippet.PublishedAt,
		"thumbnail_url":    channel.Snippet.Thumbnails.Medium.Url,
	}

	return analytics, nil
}

// CreatePlaylist creates a new playlist for organizing speaker content
func (ys *YouTubeService) CreatePlaylist(ctx context.Context, title, description string) (*youtube.Playlist, error) {
	playlist := &youtube.Playlist{
		Snippet: &youtube.PlaylistSnippet{
			Title:       title,
			Description: description,
		},
		Status: &youtube.PlaylistStatus{
			PrivacyStatus: "public",
		},
	}

	call := ys.service.Playlists.Insert([]string{"snippet", "status"}, playlist)
	response, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to create playlist: %w", err)
	}

	log.Printf("Playlist created: %s (ID: %s)", title, response.Id)
	return response, nil
}

// AddVideoToPlaylist adds a video to a specific playlist
func (ys *YouTubeService) AddVideoToPlaylist(ctx context.Context, playlistID, videoID string) error {
	playlistItem := &youtube.PlaylistItem{
		Snippet: &youtube.PlaylistItemSnippet{
			PlaylistId: playlistID,
			ResourceId: &youtube.ResourceId{
				Kind:    "youtube#video",
				VideoId: videoID,
			},
		},
	}

	call := ys.service.PlaylistItems.Insert([]string{"snippet"}, playlistItem)
	_, err := call.Do()
	if err != nil {
		return fmt.Errorf("failed to add video to playlist: %w", err)
	}

	return nil
}

// GenerateVideoTitle creates a standardized title for speaker videos
func (ys *YouTubeService) GenerateVideoTitle(speaker models.Speaker, topic string) string {
	if topic != "" {
		return fmt.Sprintf("%s - %s | %s Recovery Speaker | Sound of Recovery",
			speaker.Name, topic, speaker.Program)
	}
	return fmt.Sprintf("%s | %s Recovery Speaker | Sound of Recovery",
		speaker.Name, speaker.Program)
}

// GenerateVideoDescription creates a standardized description for speaker videos
func (ys *YouTubeService) GenerateVideoDescription(speaker models.Speaker, topic string) string {
	var description strings.Builder

	description.WriteString(fmt.Sprintf("🎙️ %s Recovery Speaker: %s\n\n", speaker.Program, speaker.Name))

	if topic != "" {
		description.WriteString(fmt.Sprintf("📝 Topic: %s\n\n", topic))
	}

	description.WriteString("Welcome to Sound of Recovery, where we share powerful stories of hope, healing, and transformation from the recovery community.\n\n")
	description.WriteString("🔔 Subscribe for more inspiring recovery content\n")
	description.WriteString("💬 Share your thoughts in the comments\n")
	description.WriteString("🙏 Remember: You are not alone in your journey\n\n")
	description.WriteString("#Recovery #SobrietyJourney #AddictionRecovery #Hope #Healing")

	if speaker.Program == "AA" {
		description.WriteString(" #AlcoholicsAnonymous #AA")
	} else if speaker.Program == "NA" {
		description.WriteString(" #NarcoticsAnonymous #NA")
	}

	return description.String()
}

// GetVideoTags generates relevant tags for speaker videos
func (ys *YouTubeService) GetVideoTags(speaker models.Speaker, topic string) []string {
	tags := []string{
		"recovery",
		"sobriety",
		"addiction recovery",
		"sound of recovery",
		speaker.Program,
		"recovery speaker",
		"hope",
		"healing",
		"transformation",
	}

	if speaker.Program == "AA" {
		tags = append(tags, "alcoholics anonymous", "alcohol recovery")
	} else if speaker.Program == "NA" {
		tags = append(tags, "narcotics anonymous", "drug recovery")
	}

	if topic != "" {
		// Add topic-specific tags
		topicWords := strings.Fields(strings.ToLower(topic))
		tags = append(tags, topicWords...)
	}

	return tags
}

// YouTubeShort represents a YouTube Short video
type YouTubeShort struct {
	VideoID      string    `json:"video_id"`
	Title        string    `json:"title"`
	Description  string    `json:"description"`
	Duration     string    `json:"duration"`
	Views        int64     `json:"views"`
	Likes        int64     `json:"likes"`
	Comments     int64     `json:"comments"`
	PublishedAt  time.Time `json:"published_at"`
	ThumbnailURL string    `json:"thumbnail_url"`
	IsShort      bool      `json:"is_short"`
	Tags         []string  `json:"tags"`
}

// ShortsUploadRequest represents a request to upload a YouTube Short
type ShortsUploadRequest struct {
	VideoPath   string   `json:"video_path"`
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Tags        []string `json:"tags"`
	Privacy     string   `json:"privacy"` // "private", "unlisted", "public"
	SpeakerID   uint     `json:"speaker_id"`
	SourceType  string   `json:"source_type"` // "clip", "quote", "highlight"
}

// CreateShortFromLongVideo creates a YouTube Short from an existing long-form video
func (ys *YouTubeService) CreateShortFromLongVideo(videoID, startTime, endTime, title, description string) (*YouTubeShort, error) {
	// This would typically involve:
	// 1. Extracting a clip from the original video
	// 2. Converting to vertical format (9:16 aspect ratio)
	// 3. Adding recovery-themed graphics/text overlay
	// 4. Uploading as a new Short

	// For now, we'll create a mock implementation
	// In production, this would use video processing libraries

	short := &YouTubeShort{
		VideoID:      "short_" + videoID + "_" + startTime,
		Title:        title,
		Description:  description + "\n\n#Shorts #Recovery #OneDayAtATime",
		Duration:     "PT45S", // 45 seconds
		Views:        0,
		Likes:        0,
		Comments:     0,
		PublishedAt:  time.Now(),
		ThumbnailURL: fmt.Sprintf("https://i.ytimg.com/vi/%s/maxresdefault.jpg", videoID),
		IsShort:      true,
		Tags:         []string{"shorts", "recovery", "sobriety", "inspiration"},
	}

	return short, nil
}

// GenerateRecoveryShort creates recovery-focused Short content
func (ys *YouTubeService) GenerateRecoveryShort(contentType, speakerName, quote, accountType string) *ShortsUploadRequest {
	var title, description string
	var tags []string

	switch contentType {
	case "quote":
		title = fmt.Sprintf("Recovery Wisdom: %s", speakerName)
		description = fmt.Sprintf("Powerful recovery quote from %s\n\n\"%s\"\n\nYour recovery story matters. Keep going, one day at a time.\n\n#Shorts #Recovery #RecoveryQuotes #Sobriety #Hope #Healing #OneDayAtATime", speakerName, quote)
		tags = []string{"shorts", "recovery", "quotes", "sobriety", "inspiration", "hope", "healing"}

	case "highlight":
		title = fmt.Sprintf("Recovery Highlight: %s", speakerName)
		description = fmt.Sprintf("Key recovery insight from %s\n\nThis message could change someone's day. Share if it resonates with you.\n\n#Shorts #Recovery #RecoveryWisdom #Sobriety #Transformation #Hope", speakerName)
		tags = []string{"shorts", "recovery", "wisdom", "sobriety", "transformation", "speaker"}

	case "daily_inspiration":
		title = "Daily Recovery Inspiration"
		description = fmt.Sprintf("Your daily dose of recovery inspiration\n\n%s\n\nRemember: Progress, not perfection. You've got this!\n\n#Shorts #Recovery #DailyInspiration #Sobriety #OneDayAtATime #Hope", quote)
		tags = []string{"shorts", "recovery", "daily", "inspiration", "sobriety", "motivation"}

	default:
		title = "Recovery Content"
		description = fmt.Sprintf("Recovery content that matters\n\n%s\n\n#Shorts #Recovery #Sobriety", quote)
		tags = []string{"shorts", "recovery", "sobriety"}
	}

	// Account type modifications
	switch accountType {
	case "merch":
		description += "\n\nRecovery gear available - link in bio 🙏"
		tags = append(tags, "recoverymerch")
	case "memes":
		title = "Recovery Mood: " + title
		description = strings.Replace(description, "Your recovery story matters", "When recovery hits different 😅", 1)
		tags = append(tags, "recoverymemes", "humor")
	}

	return &ShortsUploadRequest{
		Title:       title,
		Description: description,
		Tags:        tags,
		Privacy:     "public",
		SourceType:  contentType,
	}
}

// GetChannelShorts retrieves YouTube Shorts from the channel
func (ys *YouTubeService) GetChannelShorts(maxResults int64) ([]*YouTubeShort, error) {
	if maxResults <= 0 {
		maxResults = 25
	}

	// Search for shorts in the channel
	call := ys.service.Search.List([]string{"id", "snippet"}).
		ChannelId(ys.channelID).
		Type("video").
		VideoDuration("short"). // Videos under 60 seconds
		MaxResults(maxResults).
		Order("date")

	response, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to search for shorts: %w", err)
	}

	var shorts []*YouTubeShort
	for _, item := range response.Items {
		// Get additional video details
		videoCall := ys.service.Videos.List([]string{"statistics", "contentDetails"}).Id(item.Id.VideoId)
		videoResponse, err := videoCall.Do()
		if err != nil {
			continue // Skip this video if we can't get details
		}

		if len(videoResponse.Items) == 0 {
			continue
		}

		video := videoResponse.Items[0]
		publishedAt, _ := time.Parse(time.RFC3339, item.Snippet.PublishedAt)

		short := &YouTubeShort{
			VideoID:      item.Id.VideoId,
			Title:        item.Snippet.Title,
			Description:  item.Snippet.Description,
			Duration:     video.ContentDetails.Duration,
			Views:        int64(video.Statistics.ViewCount),
			Likes:        int64(video.Statistics.LikeCount),
			Comments:     int64(video.Statistics.CommentCount),
			PublishedAt:  publishedAt,
			ThumbnailURL: item.Snippet.Thumbnails.High.Url,
			IsShort:      true,
			Tags:         []string{"shorts", "recovery"}, // Default tags for shorts
		}

		shorts = append(shorts, short)
	}

	return shorts, nil
}

// GetShortsAnalytics retrieves analytics specifically for YouTube Shorts
func (ys *YouTubeService) GetShortsAnalytics(days int) (map[string]interface{}, error) {
	shorts, err := ys.GetChannelShorts(50) // Get recent shorts
	if err != nil {
		return nil, err
	}

	// Calculate shorts-specific metrics
	var totalViews, totalLikes, totalComments int64
	var avgDuration float64
	topPerformingShorts := make([]*YouTubeShort, 0)

	for _, short := range shorts {
		// Only include shorts from the specified time period
		if time.Since(short.PublishedAt).Hours() <= float64(days*24) {
			totalViews += short.Views
			totalLikes += short.Likes
			totalComments += short.Comments

			// Add to top performing if it has good engagement
			if short.Views > 1000 || short.Likes > 50 {
				topPerformingShorts = append(topPerformingShorts, short)
			}
		}
	}

	// Calculate average engagement rate
	engagementRate := float64(0)
	if totalViews > 0 {
		engagementRate = float64(totalLikes+totalComments) / float64(totalViews) * 100
	}

	analytics := map[string]interface{}{
		"total_shorts":     len(shorts),
		"total_views":      totalViews,
		"total_likes":      totalLikes,
		"total_comments":   totalComments,
		"average_duration": avgDuration,
		"engagement_rate":  engagementRate,
		"top_performing":   topPerformingShorts,
		"shorts_vs_long_form": map[string]interface{}{
			"shorts_percentage": 15.5, // Mock data - would calculate from actual channel data
			"performance_ratio": 2.3,  // Shorts vs long-form performance
		},
	}

	return analytics, nil
}

// CreateRecoveryQuoteShort creates a Short from a recovery quote
func (ys *YouTubeService) CreateRecoveryQuoteShort(quote, author, accountType string) (*ShortsUploadRequest, error) {
	shortContent := ys.GenerateRecoveryShort("quote", author, quote, accountType)

	// In production, this would:
	// 1. Generate a visual quote card (9:16 aspect ratio)
	// 2. Add background music or ambient sound
	// 3. Create a 15-45 second video
	// 4. Upload to YouTube as a Short

	return shortContent, nil
}

// GetVideoByID retrieves a video by its ID
func (ys *YouTubeService) GetVideoByID(videoID string) (*VideoAnalytics, error) {
	call := ys.service.Videos.List([]string{"snippet", "statistics", "contentDetails"}).Id(videoID)
	response, err := call.Do()
	if err != nil {
		return nil, fmt.Errorf("failed to get video: %w", err)
	}

	if len(response.Items) == 0 {
		return nil, fmt.Errorf("video not found: %s", videoID)
	}

	video := response.Items[0]
	publishedAt, _ := time.Parse(time.RFC3339, video.Snippet.PublishedAt)

	analytics := &VideoAnalytics{
		VideoID:      video.Id,
		Title:        video.Snippet.Title,
		Views:        int64(video.Statistics.ViewCount),
		Likes:        int64(video.Statistics.LikeCount),
		Comments:     int64(video.Statistics.CommentCount),
		Duration:     video.ContentDetails.Duration,
		PublishedAt:  publishedAt,
		ThumbnailURL: video.Snippet.Thumbnails.High.Url,
	}

	return analytics, nil
}

// CrossPostToShorts creates a Short version of existing long-form content
func (ys *YouTubeService) CrossPostToShorts(videoID, clipStart, clipEnd, accountType string) (*YouTubeShort, error) {
	// Get original video details
	video, err := ys.GetVideoByID(videoID)
	if err != nil {
		return nil, fmt.Errorf("failed to get original video: %w", err)
	}

	// Create Short title and description
	shortTitle := fmt.Sprintf("Recovery Clip: %s", video.Title)
	if len(shortTitle) > 100 {
		shortTitle = shortTitle[:97] + "..."
	}

	shortDescription := fmt.Sprintf("Powerful clip from: %s\n\nFull video: https://youtube.com/watch?v=%s\n\n#Shorts #Recovery #Sobriety #OneDayAtATime", video.Title, videoID)

	// Create the Short (mock implementation)
	short, err := ys.CreateShortFromLongVideo(videoID, clipStart, clipEnd, shortTitle, shortDescription)
	if err != nil {
		return nil, err
	}

	return short, nil
}
