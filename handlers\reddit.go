package handlers

import (
	"net/http"
	"strconv"

	"recovery-dashboard/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// RedditHandler handles Reddit-specific operations
type RedditHandler struct {
	db            *gorm.DB
	redditService *services.RedditService
}

// NewRedditHandler creates a new Reddit handler
func NewRedditHandler(db *gorm.DB, redditService *services.RedditService) *RedditHandler {
	return &RedditHandler{
		db:            db,
		redditService: redditService,
	}
}

// GetUserInfo retrieves Reddit user information
func (h *RedditHandler) GetUserInfo(c *gin.Context) {
	if h.redditService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Reddit service not available"})
		return
	}

	userInfo, err := h.redditService.GetUserInfo()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info: " + err.Error()})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"user":   userInfo,
		"status": "success",
	})
}

// GetSubredditInfo retrieves information about a specific subreddit
func (h *RedditHandler) GetSubredditInfo(c *gin.Context) {
	if h.redditService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Reddit service not available"})
		return
	}

	subredditName := c.Param("subreddit")
	if subredditName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Subreddit name is required"})
		return
	}

	subredditInfo, err := h.redditService.GetSubredditInfo(subredditName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get subreddit info: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"subreddit": subredditInfo,
		"status":    "success",
	})
}

// SubmitPost submits a new post to a subreddit
func (h *RedditHandler) SubmitPost(c *gin.Context) {
	if h.redditService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Reddit service not available"})
		return
	}

	var req struct {
		Subreddit   string `json:"subreddit" binding:"required"`
		Title       string `json:"title" binding:"required"`
		Text        string `json:"text"`
		URL         string `json:"url"`
		Kind        string `json:"kind"` // "self" for text posts, "link" for URL posts
		SendReplies bool   `json:"send_replies"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default kind based on content
	if req.Kind == "" {
		if req.URL != "" {
			req.Kind = "link"
		} else {
			req.Kind = "self"
		}
	}

	// Validate content based on kind
	if req.Kind == "self" && req.Text == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Text is required for self posts"})
		return
	}
	if req.Kind == "link" && req.URL == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "URL is required for link posts"})
		return
	}

	submitReq := services.RedditSubmitRequest{
		Subreddit:   req.Subreddit,
		Title:       req.Title,
		Text:        req.Text,
		URL:         req.URL,
		Kind:        req.Kind,
		Resubmit:    false,
		SendReplies: req.SendReplies,
	}

	postID, err := h.redditService.SubmitPost(submitReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to submit post: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"post_id": postID,
		"message": "Reddit post submitted successfully",
	})
}

// CrossPostYouTubeVideo creates a Reddit post from a YouTube video
func (h *RedditHandler) CrossPostYouTubeVideo(c *gin.Context) {
	if h.redditService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Reddit service not available"})
		return
	}

	var req struct {
		VideoID     string `json:"video_id" binding:"required"`
		VideoTitle  string `json:"video_title" binding:"required"`
		VideoURL    string `json:"video_url" binding:"required"`
		Subreddit   string `json:"subreddit" binding:"required"`
		AccountType string `json:"account_type"` // "merch", "serious"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "serious"
	}

	// Validate subreddit is recovery-focused
	recoverySubreddits := h.redditService.GetRecoverySubreddits()
	validSubreddit := false
	for _, sub := range recoverySubreddits {
		if sub.Name == req.Subreddit {
			validSubreddit = true
			break
		}
	}

	if !validSubreddit {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Subreddit not in approved recovery communities list",
			"valid_subreddits": func() []string {
				var names []string
				for _, sub := range recoverySubreddits {
					names = append(names, sub.Name)
				}
				return names
			}(),
		})
		return
	}

	// Create the cross-post
	postID, err := h.redditService.CrossPostYouTubeVideo(req.VideoTitle, req.VideoURL, req.Subreddit, req.AccountType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cross-post video: " + err.Error()})
		return
	}

	// Generate preview of what was posted
	postContent := h.redditService.GenerateRecoveryPost(req.VideoTitle, req.VideoURL, req.Subreddit, req.AccountType)

	c.JSON(http.StatusOK, gin.H{
		"success":           true,
		"post_id":           postID,
		"video_id":          req.VideoID,
		"subreddit":         req.Subreddit,
		"reddit_post_id":    postID,
		"message":           "YouTube video cross-posted to Reddit successfully",
		"generated_content": gin.H{
			"title": postContent.Title,
			"text":  postContent.Text,
		},
	})
}

// CreateRecoveryDiscussion creates a discussion post about recovery topics
func (h *RedditHandler) CreateRecoveryDiscussion(c *gin.Context) {
	if h.redditService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Reddit service not available"})
		return
	}

	var req struct {
		Topic       string `json:"topic" binding:"required"` // daily_reflection, gratitude, challenges, milestones
		Subreddit   string `json:"subreddit" binding:"required"`
		AccountType string `json:"account_type"` // "merch", "serious"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "serious"
	}

	// Validate topic
	validTopics := []string{"daily_reflection", "gratitude", "challenges", "milestones", "general"}
	validTopic := false
	for _, topic := range validTopics {
		if topic == req.Topic {
			validTopic = true
			break
		}
	}

	if !validTopic {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":        "Invalid topic",
			"valid_topics": validTopics,
		})
		return
	}

	// Create the discussion post
	postID, err := h.redditService.CreateRecoveryDiscussion(req.Topic, req.Subreddit, req.AccountType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create discussion: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"post_id":    postID,
		"topic":      req.Topic,
		"subreddit":  req.Subreddit,
		"message":    "Recovery discussion created successfully",
	})
}

// GetRecoverySubreddits returns a list of recovery-focused subreddits
func (h *RedditHandler) GetRecoverySubreddits(c *gin.Context) {
	if h.redditService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Reddit service not available"})
		return
	}

	subreddits := h.redditService.GetRecoverySubreddits()

	c.JSON(http.StatusOK, gin.H{
		"subreddits": subreddits,
		"count":      len(subreddits),
		"status":     "success",
	})
}

// GetSubredditGuidelines returns posting guidelines for a specific subreddit
func (h *RedditHandler) GetSubredditGuidelines(c *gin.Context) {
	if h.redditService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Reddit service not available"})
		return
	}

	subredditName := c.Param("subreddit")
	if subredditName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Subreddit name is required"})
		return
	}

	guidelines := h.redditService.GetSubredditGuidelines(subredditName)

	c.JSON(http.StatusOK, gin.H{
		"subreddit":  subredditName,
		"guidelines": guidelines,
		"status":     "success",
	})
}

// GetAnalytics retrieves Reddit analytics
func (h *RedditHandler) GetAnalytics(c *gin.Context) {
	if h.redditService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Reddit service not available"})
		return
	}

	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		days = 30
	}

	analytics, err := h.redditService.GetAnalytics(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get analytics: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"analytics":   analytics,
		"period_days": days,
		"status":      "success",
	})
}

// ValidateToken checks if the Reddit credentials are valid
func (h *RedditHandler) ValidateToken(c *gin.Context) {
	if h.redditService == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{"error": "Reddit service not available"})
		return
	}

	err := h.redditService.ValidateToken()
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"valid": false,
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"valid":   true,
		"message": "Reddit credentials are valid",
	})
}
