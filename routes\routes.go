package routes

import (
	"recovery-dashboard/config"
	"recovery-dashboard/handlers"
	"recovery-dashboard/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func SetupRoutes(router *gin.Engine, db *gorm.DB, cfg *config.Config) {
	// Initialize handlers (they will handle nil db gracefully)
	contentHandler := handlers.NewContentHandler(db)
	shopifyHandler := handlers.NewShopifyHandler(db, cfg)
	socialHandler := handlers.NewSocialHandler(db)
	metricsHandler := handlers.NewMetricsHandler(db)
	userHandler := handlers.NewUserHandler(db, cfg)
	youtubeHandler := handlers.NewYouTubeHandler(db, cfg)

	// Initialize TikTok handler
	var tiktokHandler *handlers.TikTokHandler
	if cfg.TikTokRecoveryMerchToken != "" {
		tiktokService := services.NewTikTokService(cfg.TikTokRecoveryMerchToken)
		tiktokHandler = handlers.NewTikTokHandler(db, tiktokService)
	}

	// Initialize additional social media services
	var snapchatService *services.SnapchatService
	var threadsService *services.ThreadsService
	var xService *services.XService
	var instagramService *services.InstagramService
	var pinterestService *services.PinterestService
	var multiPlatformHandler *handlers.MultiPlatformSocialHandler

	if cfg.SnapchatToken != "" {
		snapchatService = services.NewSnapchatService(cfg.SnapchatToken)
	}
	if cfg.ThreadsToken != "" {
		threadsService = services.NewThreadsService(cfg.ThreadsToken)
	}
	if cfg.XToken != "" {
		xService = services.NewXService(cfg.XToken)
	}
	if cfg.InstagramToken != "" {
		instagramService = services.NewInstagramService(cfg.InstagramToken)
	}
	if cfg.PinterestToken != "" {
		pinterestService = services.NewPinterestService(cfg.PinterestToken)
	}

	// Create multi-platform handler if any additional platforms are configured
	if snapchatService != nil || threadsService != nil || xService != nil || instagramService != nil || pinterestService != nil {
		multiPlatformHandler = handlers.NewMultiPlatformSocialHandler(db, snapchatService, threadsService, xService, instagramService, pinterestService)
	}

	// Initialize visual content service
	visualContentService := services.NewVisualContentService("./static/generated", "/static/generated")

	// Initialize Instagram handler
	var instagramHandler *handlers.InstagramHandler
	if instagramService != nil {
		instagramHandler = handlers.NewInstagramHandler(db, instagramService, visualContentService)
	}

	// Initialize Pinterest handler
	var pinterestHandler *handlers.PinterestHandler
	if pinterestService != nil {
		pinterestHandler = handlers.NewPinterestHandler(db, pinterestService, visualContentService)
	}

	// Initialize Facebook multi-page service and handler
	var facebookHandler *handlers.FacebookHandler
	multiPageFacebookService := services.NewMultiPageFacebookService()

	// Add configured Facebook pages
	if cfg.FacebookSoundOfRecoveryToken != "" && cfg.FacebookSoundOfRecoveryID != "" {
		multiPageFacebookService.AddPage(
			cfg.FacebookSoundOfRecoveryID,
			"Sound of Recovery",
			cfg.FacebookSoundOfRecoveryToken,
			"serious",
			"Thoughtful, inspirational recovery content focusing on hope and healing",
		)
	}

	if cfg.FacebookRecoveryMemesToken != "" && cfg.FacebookRecoveryMemesID != "" {
		multiPageFacebookService.AddPage(
			cfg.FacebookRecoveryMemesID,
			"Recovery Memes",
			cfg.FacebookRecoveryMemesToken,
			"humor",
			"Humorous recovery content that brings lightness to the journey",
		)
	}

	// Create handlers if we have at least one page configured
	var multiPageFacebookHandler *handlers.MultiPageFacebookHandler
	if len(multiPageFacebookService.GetAllPages()) > 0 {
		multiPageFacebookHandler = handlers.NewMultiPageFacebookHandler(db, multiPageFacebookService)

		// Also create single-page handler for backward compatibility
		defaultToken := cfg.FacebookToken
		if defaultToken == "" && cfg.FacebookSoundOfRecoveryToken != "" {
			defaultToken = cfg.FacebookSoundOfRecoveryToken
		}
		if defaultToken != "" {
			facebookService := services.NewFacebookService(defaultToken)
			facebookHandler = handlers.NewFacebookHandler(db, facebookService)
		}
	}

	// Health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Authentication routes
		auth := v1.Group("/auth")
		{
			auth.POST("/login", userHandler.Login)
			auth.POST("/register", userHandler.Register)
			auth.POST("/refresh", userHandler.RefreshToken)
		}

		// Content Pipeline routes
		content := v1.Group("/content")
		{
			// Speakers
			content.GET("/speakers", contentHandler.GetSpeakers)
			content.POST("/speakers", contentHandler.CreateSpeaker)
			content.GET("/speakers/:id", contentHandler.GetSpeaker)
			content.PATCH("/speakers/:id", contentHandler.UpdateSpeaker)
			content.DELETE("/speakers/:id", contentHandler.DeleteSpeaker)

			// Videos
			content.GET("/videos", contentHandler.GetVideos)
			content.POST("/videos", contentHandler.CreateVideo)
			content.GET("/videos/:id", contentHandler.GetVideo)
			content.PATCH("/videos/:id", contentHandler.UpdateVideo)
			content.DELETE("/videos/:id", contentHandler.DeleteVideo)

			// Pipeline operations
			content.PATCH("/pipeline/:id", contentHandler.UpdatePipelineStatus)
			content.POST("/pipeline/:id/process", contentHandler.ProcessContent)
		}

		// Shopify Integration routes
		shopify := v1.Group("/shopify")
		{
			shopify.GET("/orders", shopifyHandler.GetOrders)
			shopify.GET("/orders/:id", shopifyHandler.GetOrder)
			shopify.POST("/orders/sync", shopifyHandler.SyncOrders)

			shopify.GET("/products", shopifyHandler.GetProducts)
			shopify.GET("/products/:id", shopifyHandler.GetProduct)
			shopify.POST("/products/sync", shopifyHandler.SyncProducts)

			shopify.POST("/promos", shopifyHandler.CreatePromotion)
			shopify.GET("/promos", shopifyHandler.GetPromotions)
		}

		// Social Media Scheduler routes
		social := v1.Group("/social")
		{
			// Posts
			social.GET("/posts", socialHandler.GetPosts)
			social.POST("/posts", socialHandler.CreatePost)
			social.GET("/posts/:id", socialHandler.GetPost)
			social.PATCH("/posts/:id", socialHandler.UpdatePost)
			social.DELETE("/posts/:id", socialHandler.DeletePost)

			// Scheduling
			social.POST("/posts/:id/schedule", socialHandler.SchedulePost)
			social.POST("/posts/:id/publish", socialHandler.PublishPost)
			social.GET("/posts/scheduled", socialHandler.GetScheduledPosts)

			// Brands
			social.GET("/brands", socialHandler.GetBrands)
			social.POST("/brands", socialHandler.CreateBrand)
			social.PATCH("/brands/:id", socialHandler.UpdateBrand)
		}

		// Metrics and Analytics routes
		metrics := v1.Group("/metrics")
		{
			metrics.GET("/", metricsHandler.GetDashboardMetrics)
			metrics.GET("/content", metricsHandler.GetContentMetrics)
			metrics.GET("/shopify", metricsHandler.GetShopifyMetrics)
			metrics.GET("/social", metricsHandler.GetSocialMetrics)
			metrics.GET("/audience", metricsHandler.GetAudienceMetrics)
		}

		// User Management routes
		users := v1.Group("/users")
		{
			users.GET("/", userHandler.GetUsers)
			users.POST("/", userHandler.CreateUser)
			users.GET("/:id", userHandler.GetUser)
			users.PATCH("/:id", userHandler.UpdateUser)
			users.DELETE("/:id", userHandler.DeleteUser)
			users.PATCH("/:id/permissions", userHandler.UpdatePermissions)
		}

		// Readings routes
		readings := v1.Group("/readings")
		{
			readings.GET("/", contentHandler.GetReadings)
			readings.POST("/", contentHandler.CreateReading)
			readings.GET("/:id", contentHandler.GetReading)
			readings.PATCH("/:id", contentHandler.UpdateReading)
			readings.GET("/today", contentHandler.GetTodayReading)
		}

		// YouTube Integration routes
		youtube := v1.Group("/youtube")
		{
			// Video management
			youtube.POST("/upload", youtubeHandler.UploadVideo)
			youtube.GET("/videos", youtubeHandler.GetChannelVideos)
			youtube.GET("/videos/:video_id/analytics", youtubeHandler.GetVideoAnalytics)
			youtube.GET("/channel/analytics", youtubeHandler.GetChannelAnalytics)

			// Playlist management
			youtube.POST("/playlists", youtubeHandler.CreatePlaylist)
			youtube.POST("/playlists/add-video", youtubeHandler.AddVideoToPlaylist)

			// Content pipeline integration
			youtube.POST("/speakers/:speaker_id/process", youtubeHandler.ProcessSpeakerVideo)
			youtube.GET("/upload-status", youtubeHandler.GetUploadStatus)
		}

		// Facebook Integration routes
		if facebookHandler != nil {
			facebook := v1.Group("/facebook")
			{
				// Token validation
				facebook.GET("/validate", facebookHandler.ValidateToken)

				// Page management
				facebook.GET("/pages", facebookHandler.GetManagedPages)
				facebook.GET("/pages/:page_id", facebookHandler.GetPageInfo)
				facebook.GET("/pages/:page_id/analytics", facebookHandler.GetPageAnalytics)
				facebook.GET("/pages/:page_id/insights", facebookHandler.GetPageInsights)

				// Post management
				facebook.GET("/pages/:page_id/posts", facebookHandler.GetPagePosts)
				facebook.POST("/pages/:page_id/posts", facebookHandler.CreatePost)

				// Cross-platform integration
				facebook.POST("/pages/:page_id/cross-post-youtube", facebookHandler.CrossPostYouTubeVideo)
			}
		}

		// Multi-Page Facebook Integration routes
		if multiPageFacebookHandler != nil {
			multiPageFacebook := v1.Group("/facebook-multi")
			{
				// Multi-page management
				multiPageFacebook.GET("/pages", multiPageFacebookHandler.GetAllPages)
				multiPageFacebook.GET("/pages/status", multiPageFacebookHandler.GetAllPagesStatus)

				// Individual page operations
				multiPageFacebook.GET("/pages/:page_id", multiPageFacebookHandler.GetPageInfo)
				multiPageFacebook.GET("/pages/:page_id/posts", multiPageFacebookHandler.GetPagePosts)
				multiPageFacebook.GET("/pages/:page_id/insights", multiPageFacebookHandler.GetPageInsights)
				multiPageFacebook.POST("/pages/:page_id/posts", multiPageFacebookHandler.CreatePost)

				// Cross-platform integration
				multiPageFacebook.POST("/pages/:page_id/cross-post-youtube", multiPageFacebookHandler.CrossPostYouTubeVideo)
				multiPageFacebook.POST("/cross-post-all", multiPageFacebookHandler.CrossPostToAllPages)
			}
		}

		// TikTok Integration routes
		if tiktokHandler != nil {
			tiktok := v1.Group("/tiktok")
			{
				// Token validation
				tiktok.GET("/validate", tiktokHandler.ValidateToken)

				// User and account management
				tiktok.GET("/user", tiktokHandler.GetUserInfo)
				tiktok.GET("/analytics", tiktokHandler.GetAccountAnalytics)

				// Video management
				tiktok.GET("/videos", tiktokHandler.GetUserVideos)
				tiktok.POST("/videos", tiktokHandler.UploadVideo)

				// Cross-platform integration
				tiktok.POST("/cross-post-youtube", tiktokHandler.CrossPostYouTubeVideo)
				tiktok.POST("/content-preview", tiktokHandler.GenerateContentPreview)
			}
		}

		// Instagram Integration routes
		if instagramHandler != nil {
			instagram := v1.Group("/instagram")
			{
				// Token validation and user info
				instagram.GET("/validate", instagramHandler.ValidateToken)
				instagram.GET("/user", instagramHandler.GetUserInfo)
				instagram.GET("/analytics", instagramHandler.GetAnalytics)

				// Media management
				instagram.GET("/media", instagramHandler.GetUserMedia)
				instagram.POST("/posts", instagramHandler.CreatePost)

				// Recovery-specific content
				instagram.POST("/quote-post", instagramHandler.CreateRecoveryQuotePost)

				// Cross-platform integration
				instagram.POST("/cross-post-youtube", instagramHandler.CrossPostYouTubeVideo)
			}
		}

		// Pinterest Integration routes
		if pinterestHandler != nil {
			pinterest := v1.Group("/pinterest")
			{
				// Token validation and user info
				pinterest.GET("/validate", pinterestHandler.ValidateToken)
				pinterest.GET("/user", pinterestHandler.GetUserInfo)
				pinterest.GET("/analytics", pinterestHandler.GetAnalytics)

				// Board management
				pinterest.GET("/boards", pinterestHandler.GetBoards)
				pinterest.POST("/boards", pinterestHandler.CreateBoard)
				pinterest.POST("/boards/setup-recovery", pinterestHandler.SetupRecoveryBoards)
				pinterest.GET("/boards/:boardId/pins", pinterestHandler.GetBoardPins)

				// Pin management
				pinterest.POST("/pins", pinterestHandler.CreatePin)

				// Recovery-specific content
				pinterest.POST("/quote-pin", pinterestHandler.CreateRecoveryQuotePin)

				// Cross-platform integration
				pinterest.POST("/cross-post-youtube", pinterestHandler.CrossPostYouTubeVideo)
			}
		}

		// Multi-Platform Social Media Integration routes
		if multiPlatformHandler != nil {
			multiPlatform := v1.Group("/social-multi")
			{
				// Platform status and management
				multiPlatform.GET("/platforms/status", multiPlatformHandler.GetAllPlatformsStatus)
				multiPlatform.GET("/analytics", multiPlatformHandler.GetPlatformAnalytics)

				// Cross-platform posting
				multiPlatform.POST("/cross-post-all", multiPlatformHandler.CrossPostToAllPlatforms)
				multiPlatform.POST("/content-preview", multiPlatformHandler.GenerateContentPreview)
			}
		}
	}

	// Serve static files (for frontend if using HTMX/AlpineJS)
	router.Static("/static", "./static")
	router.LoadHTMLGlob("templates/*")

	// Frontend routes (if using server-side rendering)
	router.GET("/", func(c *gin.Context) {
		c.HTML(200, "dashboard.html", gin.H{
			"title": "Recovery Dashboard",
		})
	})
}
