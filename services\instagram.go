package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// InstagramService handles Instagram API interactions
type InstagramService struct {
	AccessToken string
	BaseURL     string
}

// NewInstagramService creates a new Instagram service instance
func NewInstagramService(accessToken string) *InstagramService {
	return &InstagramService{
		AccessToken: accessToken,
		BaseURL:     "https://graph.instagram.com",
	}
}

// InstagramUser represents Instagram user information
type InstagramUser struct {
	ID                string `json:"id"`
	Username          string `json:"username"`
	AccountType       string `json:"account_type"` // PERSONAL, BUSINESS, CREATOR
	MediaCount        int    `json:"media_count"`
	FollowersCount    int    `json:"followers_count"`
	FollowsCount      int    `json:"follows_count"`
	Name              string `json:"name"`
	Biography         string `json:"biography"`
	Website           string `json:"website"`
	ProfilePictureURL string `json:"profile_picture_url"`
}

// InstagramMedia represents Instagram media (post, story, reel)
type InstagramMedia struct {
	ID            string    `json:"id"`
	MediaType     string    `json:"media_type"` // IMAGE, VIDEO, CAROUSEL_ALBUM
	MediaURL      string    `json:"media_url"`
	Permalink     string    `json:"permalink"`
	Caption       string    `json:"caption"`
	Timestamp     time.Time `json:"timestamp"`
	LikeCount     int       `json:"like_count"`
	CommentsCount int       `json:"comments_count"`
	Username      string    `json:"username"`
}

// InstagramStory represents Instagram story
type InstagramStory struct {
	ID        string    `json:"id"`
	MediaType string    `json:"media_type"` // IMAGE, VIDEO
	MediaURL  string    `json:"media_url"`
	Timestamp time.Time `json:"timestamp"`
}

// InstagramAnalytics represents Instagram analytics data
type InstagramAnalytics struct {
	Impressions    int `json:"impressions"`
	Reach          int `json:"reach"`
	ProfileViews   int `json:"profile_views"`
	WebsiteClicks  int `json:"website_clicks"`
	EmailContacts  int `json:"email_contacts"`
	PhoneContacts  int `json:"phone_contacts"`
	GetDirections  int `json:"get_directions"`
	TextMessages   int `json:"text_messages"`
	FollowerCount  int `json:"follower_count"`
	FollowingCount int `json:"following_count"`
}

// InstagramCreatePostRequest represents a request to create an Instagram post
type InstagramCreatePostRequest struct {
	ImageURL    string `json:"image_url,omitempty"`
	VideoURL    string `json:"video_url,omitempty"`
	Caption     string `json:"caption"`
	MediaType   string `json:"media_type"` // IMAGE, VIDEO, CAROUSEL_ALBUM
	AccessToken string `json:"access_token"`
}

// InstagramCreateStoryRequest represents a request to create an Instagram story
type InstagramCreateStoryRequest struct {
	MediaURL    string `json:"media_url"`
	MediaType   string `json:"media_type"` // IMAGE, VIDEO
	AccessToken string `json:"access_token"`
}

// GetUserInfo retrieves Instagram user information
func (is *InstagramService) GetUserInfo() (*InstagramUser, error) {
	url := fmt.Sprintf("%s/me?fields=id,username,account_type,media_count,followers_count,follows_count,name,biography,website,profile_picture_url&access_token=%s",
		is.BaseURL, is.AccessToken)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Instagram API error: %s", string(body))
	}

	var user InstagramUser
	if err := json.NewDecoder(resp.Body).Decode(&user); err != nil {
		return nil, fmt.Errorf("failed to decode user info: %w", err)
	}

	return &user, nil
}

// CreatePost creates an Instagram post
func (is *InstagramService) CreatePost(req InstagramCreatePostRequest) (string, error) {
	// Step 1: Create media container
	containerURL := fmt.Sprintf("%s/me/media", is.BaseURL)

	containerData := map[string]string{
		"caption":      req.Caption,
		"access_token": is.AccessToken,
	}

	if req.MediaType == "IMAGE" && req.ImageURL != "" {
		containerData["image_url"] = req.ImageURL
	} else if req.MediaType == "VIDEO" && req.VideoURL != "" {
		containerData["video_url"] = req.VideoURL
		containerData["media_type"] = "VIDEO"
	} else {
		return "", fmt.Errorf("invalid media type or missing media URL")
	}

	jsonBody, err := json.Marshal(containerData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal container request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", containerURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create container request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to create media container: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Instagram API error creating container: %s", string(body))
	}

	var containerResponse struct {
		ID string `json:"id"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&containerResponse); err != nil {
		return "", fmt.Errorf("failed to decode container response: %w", err)
	}

	// Step 2: Publish the media container
	publishURL := fmt.Sprintf("%s/me/media_publish", is.BaseURL)
	publishData := map[string]string{
		"creation_id":  containerResponse.ID,
		"access_token": is.AccessToken,
	}

	publishBody, err := json.Marshal(publishData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal publish request: %w", err)
	}

	publishReq, err := http.NewRequest("POST", publishURL, bytes.NewBuffer(publishBody))
	if err != nil {
		return "", fmt.Errorf("failed to create publish request: %w", err)
	}

	publishReq.Header.Set("Content-Type", "application/json")

	publishResp, err := client.Do(publishReq)
	if err != nil {
		return "", fmt.Errorf("failed to publish media: %w", err)
	}
	defer publishResp.Body.Close()

	if publishResp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(publishResp.Body)
		return "", fmt.Errorf("Instagram API error publishing: %s", string(body))
	}

	var publishResponse struct {
		ID string `json:"id"`
	}

	if err := json.NewDecoder(publishResp.Body).Decode(&publishResponse); err != nil {
		return "", fmt.Errorf("failed to decode publish response: %w", err)
	}

	return publishResponse.ID, nil
}

// GetUserMedia retrieves user's Instagram media
func (is *InstagramService) GetUserMedia(limit int) ([]InstagramMedia, error) {
	url := fmt.Sprintf("%s/me/media?fields=id,media_type,media_url,permalink,caption,timestamp,like_count,comments_count,username&limit=%d&access_token=%s",
		is.BaseURL, limit, is.AccessToken)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get media: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Instagram API error: %s", string(body))
	}

	var response struct {
		Data []InstagramMedia `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode media: %w", err)
	}

	return response.Data, nil
}

// GetAnalytics retrieves Instagram analytics
func (is *InstagramService) GetAnalytics(days int) (*InstagramAnalytics, error) {
	// Mock analytics for now since Instagram analytics require business account
	analytics := &InstagramAnalytics{
		Impressions:    25600,
		Reach:          18900,
		ProfileViews:   1250,
		WebsiteClicks:  456,
		EmailContacts:  23,
		PhoneContacts:  12,
		GetDirections:  8,
		TextMessages:   15,
		FollowerCount:  3450,
		FollowingCount: 890,
	}

	return analytics, nil
}

// ValidateToken checks if the Instagram access token is valid
func (is *InstagramService) ValidateToken() error {
	_, err := is.GetUserInfo()
	return err
}

// GenerateRecoveryPost creates Instagram-appropriate content from YouTube videos
func (is *InstagramService) GenerateRecoveryPost(videoTitle, videoURL, accountType string) InstagramCreatePostRequest {
	var caption string

	// Base hashtags for recovery content
	baseHashtags := "#Recovery #Sobriety #OneDayAtATime #SoundOfRecovery #RecoveryJourney #Hope #Healing #AA #NA #RecoveryCommunity"

	switch accountType {
	case "merch":
		caption = fmt.Sprintf("🔥 New recovery content + merch drop!\n\n%s\n\nRecovery gear that represents your journey 🙏\n\nLink in bio for merch 👆\n\n%s #RecoveryMerch #RecoveryGear #SoberStyle",
			videoTitle, baseHashtags)
	case "memes":
		caption = fmt.Sprintf("Recovery mood 😅\n\n%s\n\nWhen the program hits different 💯\n\nTag someone who needs this! 👇\n\n%s #RecoveryMemes #SoberLife #RecoveryHumor",
			videoTitle, baseHashtags)
	case "serious":
		caption = fmt.Sprintf("Powerful recovery wisdom 🙏\n\n%s\n\nEvery story matters. Every day counts. Keep going.\n\n💬 Share your thoughts below\n\n%s #RecoveryWisdom #Inspiration",
			videoTitle, baseHashtags)
	default:
		caption = fmt.Sprintf("New recovery content:\n\n%s\n\nYour recovery story matters 🙏\n\n%s",
			videoTitle, baseHashtags)
	}

	// Instagram allows up to 2,200 characters in captions
	if len(caption) > 2200 {
		caption = caption[:2197] + "..."
	}

	return InstagramCreatePostRequest{
		Caption:   caption,
		MediaType: "IMAGE", // Default to image, can be overridden
	}
}

// GenerateRecoveryStory creates Instagram story content from YouTube videos
func (is *InstagramService) GenerateRecoveryStory(videoTitle, videoURL, accountType string) InstagramCreateStoryRequest {
	// Stories don't have captions in the same way, but we can use stickers/text overlays
	// For now, we'll focus on the media aspect
	return InstagramCreateStoryRequest{
		MediaType: "IMAGE", // Default to image, can be overridden with video
	}
}

// CrossPostYouTubeVideo creates an Instagram post for a YouTube video
func (is *InstagramService) CrossPostYouTubeVideo(videoTitle, videoURL, accountType string) (string, error) {
	postContent := is.GenerateRecoveryPost(videoTitle, videoURL, accountType)

	// For cross-posting, we'll need to generate or use a thumbnail image
	// This would typically involve creating a visual representation of the video
	// For now, we'll return an error indicating that media URL is required
	if postContent.ImageURL == "" && postContent.VideoURL == "" {
		return "", fmt.Errorf("media URL required for Instagram post - visual content generation needed")
	}

	return is.CreatePost(postContent)
}

// CreateRecoveryQuotePost creates a recovery quote post with generated visual
func (is *InstagramService) CreateRecoveryQuotePost(quote, author, imageURL, accountType string) (string, error) {
	var caption string
	baseHashtags := "#Recovery #Sobriety #RecoveryQuotes #Inspiration #Hope #Healing #OneDayAtATime"

	switch accountType {
	case "merch":
		caption = fmt.Sprintf("💫 Recovery wisdom for your journey\n\n\"%s\"\n\n- %s\n\nRecovery gear in bio 🙏\n\n%s #RecoveryMerch",
			quote, author, baseHashtags)
	case "serious":
		caption = fmt.Sprintf("🙏 Words of wisdom\n\n\"%s\"\n\n- %s\n\nReflect on this today 💭\n\n%s #RecoveryWisdom",
			quote, author, baseHashtags)
	default:
		caption = fmt.Sprintf("\"%s\"\n\n- %s\n\n%s", quote, author, baseHashtags)
	}

	postReq := InstagramCreatePostRequest{
		ImageURL:  imageURL,
		Caption:   caption,
		MediaType: "IMAGE",
	}

	return is.CreatePost(postReq)
}
