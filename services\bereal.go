package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// BeRealService handles BeReal API interactions
type BeRealService struct {
	AccessToken string
	BaseURL     string
	UserID      string
}

// NewBeRealService creates a new BeReal service instance
func NewBeRealService(accessToken, userID string) *BeRealService {
	return &BeRealService{
		AccessToken: accessToken,
		BaseURL:     "https://mobile.bereal.com/api",
		UserID:      userID,
	}
}

// BeRealUser represents BeReal user information
type BeRealUser struct {
	ID             string    `json:"id"`
	Username       string    `json:"username"`
	FullName       string    `json:"fullname"`
	ProfilePicture string    `json:"profilePicture"`
	Biography      string    `json:"biography"`
	Location       string    `json:"location"`
	FriendsCount   int       `json:"friendsCount"`
	FollowersCount int       `json:"followersCount"`
	FollowingCount int       `json:"followingCount"`
	PostsCount     int       `json:"postsCount"`
	CreatedAt      time.Time `json:"createdAt"`
	IsVerified     bool      `json:"isVerified"`
	IsPrivate      bool      `json:"isPrivate"`
}

// BeRealPost represents a BeReal post
type BeRealPost struct {
	ID                string    `json:"id"`
	UserID            string    `json:"userId"`
	Username          string    `json:"username"`
	Caption           string    `json:"caption"`
	Location          string    `json:"location"`
	FrontPhoto        string    `json:"frontPhoto"`
	BackPhoto         string    `json:"backPhoto"`
	CreatedAt         time.Time `json:"createdAt"`
	UpdatedAt         time.Time `json:"updatedAt"`
	IsLate            bool      `json:"isLate"`
	LateInSeconds     int       `json:"lateInSeconds"`
	ReactionCount     int       `json:"reactionCount"`
	CommentCount      int       `json:"commentCount"`
	Visibility        string    `json:"visibility"` // "friends", "public"
	IsRecoveryPost    bool      `json:"isRecoveryPost"`
	RecoveryMilestone string    `json:"recoveryMilestone,omitempty"`
}

// BeRealMoment represents a BeReal moment/notification
type BeRealMoment struct {
	ID        string    `json:"id"`
	Region    string    `json:"region"`
	CreatedAt time.Time `json:"createdAt"`
	ExpiresAt time.Time `json:"expiresAt"`
	IsActive  bool      `json:"isActive"`
	TimeLeft  int       `json:"timeLeft"` // seconds
}

// BeRealAnalytics represents BeReal analytics data
type BeRealAnalytics struct {
	TotalPosts       int     `json:"total_posts"`
	TotalReactions   int     `json:"total_reactions"`
	TotalComments    int     `json:"total_comments"`
	AverageReactions float64 `json:"average_reactions"`
	OnTimePostsCount int     `json:"on_time_posts"`
	LatePostsCount   int     `json:"late_posts"`
	OnTimePercentage float64 `json:"on_time_percentage"`
	FriendsGrowth    int     `json:"friends_growth"`
	EngagementRate   float64 `json:"engagement_rate"`
	RecoveryPosts    int     `json:"recovery_posts"`
	MilestoneShares  int     `json:"milestone_shares"`
}

// BeRealCreatePostRequest represents a request to create a BeReal post
type BeRealCreatePostRequest struct {
	Caption           string `json:"caption"`
	FrontPhotoPath    string `json:"frontPhotoPath"`
	BackPhotoPath     string `json:"backPhotoPath"`
	Location          string `json:"location,omitempty"`
	Visibility        string `json:"visibility"` // "friends", "public"
	IsRecoveryPost    bool   `json:"isRecoveryPost"`
	RecoveryMilestone string `json:"recoveryMilestone,omitempty"`
}

// GetUserInfo retrieves BeReal user information
func (bs *BeRealService) GetUserInfo() (*BeRealUser, error) {
	url := fmt.Sprintf("%s/person/me", bs.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+bs.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("BeReal API error: %s", string(body))
	}

	var user BeRealUser
	if err := json.NewDecoder(resp.Body).Decode(&user); err != nil {
		return nil, fmt.Errorf("failed to decode user info: %w", err)
	}

	return &user, nil
}

// GetCurrentMoment retrieves the current BeReal moment
func (bs *BeRealService) GetCurrentMoment() (*BeRealMoment, error) {
	url := fmt.Sprintf("%s/feeds/moments", bs.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+bs.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get moment: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("BeReal API error: %s", string(body))
	}

	var response struct {
		Moment BeRealMoment `json:"moment"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode moment: %w", err)
	}

	return &response.Moment, nil
}

// CreatePost creates a BeReal post
func (bs *BeRealService) CreatePost(req BeRealCreatePostRequest) (string, error) {
	url := fmt.Sprintf("%s/content/posts", bs.BaseURL)

	// In a real implementation, this would:
	// 1. Upload the front and back photos
	// 2. Create the post with the uploaded photo URLs
	// For now, we'll create a mock implementation

	postData := map[string]interface{}{
		"caption":        req.Caption,
		"visibility":     req.Visibility,
		"location":       req.Location,
		"isRecoveryPost": req.IsRecoveryPost,
	}

	if req.RecoveryMilestone != "" {
		postData["recoveryMilestone"] = req.RecoveryMilestone
	}

	jsonBody, err := json.Marshal(postData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+bs.AccessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to create post: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("BeReal API error: %s", string(body))
	}

	var response struct {
		ID string `json:"id"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	return response.ID, nil
}

// GetUserPosts retrieves user's BeReal posts
func (bs *BeRealService) GetUserPosts(limit int) ([]BeRealPost, error) {
	url := fmt.Sprintf("%s/feeds/friends?limit=%d", bs.BaseURL, limit)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+bs.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get posts: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("BeReal API error: %s", string(body))
	}

	var response struct {
		Posts []BeRealPost `json:"posts"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode posts: %w", err)
	}

	return response.Posts, nil
}

// GetAnalytics retrieves BeReal analytics (mock implementation)
func (bs *BeRealService) GetAnalytics(days int) (*BeRealAnalytics, error) {
	// Mock analytics for now - in production, this would aggregate user's posts and engagement
	analytics := &BeRealAnalytics{
		TotalPosts:       28,
		TotalReactions:   156,
		TotalComments:    89,
		AverageReactions: 5.6,
		OnTimePostsCount: 22,
		LatePostsCount:   6,
		OnTimePercentage: 78.6,
		FriendsGrowth:    12,
		EngagementRate:   8.7,
		RecoveryPosts:    15,
		MilestoneShares:  3,
	}

	return analytics, nil
}

// ValidateToken checks if the BeReal access token is valid
func (bs *BeRealService) ValidateToken() error {
	_, err := bs.GetUserInfo()
	return err
}

// GenerateRecoveryMoment creates authentic recovery content for BeReal
func (bs *BeRealService) GenerateRecoveryMoment(momentType, milestone, accountType string) BeRealCreatePostRequest {
	var caption string
	var isRecoveryPost = true

	switch momentType {
	case "daily_check_in":
		caption = fmt.Sprintf("Another day in recovery 🙏\n\nGrateful for this moment, this breath, this choice to keep going.\n\n#Recovery #OneDayAtATime #Grateful")

	case "milestone":
		if milestone != "" {
			caption = fmt.Sprintf("🎉 %s in recovery!\n\nEvery day is a gift. Grateful for this journey and everyone who's been part of it.\n\n#Recovery #Milestone #Grateful #OneDayAtATime", milestone)
		} else {
			caption = "Recovery milestone reached! 🎉\n\nCelebrating progress, not perfection.\n\n#Recovery #Milestone #Progress"
		}

	case "meeting":
		caption = "Just left a recovery meeting 💙\n\nThere's something powerful about being in a room with people who understand. Connection heals.\n\n#Recovery #Meeting #Community #Connection"

	case "gratitude":
		caption = "Gratitude moment 🌟\n\nIn recovery, I've learned that gratitude isn't just an attitude—it's a practice that saves my life daily.\n\n#Recovery #Gratitude #Blessed #OneDayAtATime"

	case "challenge":
		caption = "Tough day, but I'm here 💪\n\nRecovery isn't always easy, but it's always worth it. One moment at a time.\n\n#Recovery #Strength #OneDayAtATime #KeepGoing"

	case "service":
		caption = "Giving back today 🤝\n\nService is such a huge part of my recovery. When I help others, I help myself.\n\n#Recovery #Service #GivingBack #Community"

	case "nature":
		caption = "Finding peace in nature 🌿\n\nRecovery has taught me to appreciate the simple, beautiful moments. This is one of them.\n\n#Recovery #Nature #Peace #Mindfulness"

	case "workout":
		caption = "Recovery + fitness = 💯\n\nTaking care of my body is part of taking care of my recovery. Endorphins > everything.\n\n#Recovery #Fitness #HealthyLiving #Endorphins"

	default:
		caption = "Living in recovery today 🙏\n\nAuthentic moment from my recovery journey.\n\n#Recovery #Authentic #OneDayAtATime"
	}

	// Account type modifications
	switch accountType {
	case "merch":
		caption += "\n\nRecovery gear that represents the journey - check bio 👆"
	case "memes":
		// Keep it authentic for BeReal - memes don't fit the platform's vibe
		break
	}

	return BeRealCreatePostRequest{
		Caption:           caption,
		Visibility:        "friends", // BeReal is more intimate, default to friends
		IsRecoveryPost:    isRecoveryPost,
		RecoveryMilestone: milestone,
	}
}

// CreateDailyRecoveryMoment creates a daily recovery check-in post
func (bs *BeRealService) CreateDailyRecoveryMoment(milestone, accountType string) (string, error) {
	momentContent := bs.GenerateRecoveryMoment("daily_check_in", milestone, accountType)
	return bs.CreatePost(momentContent)
}

// CreateMilestonePost creates a recovery milestone celebration post
func (bs *BeRealService) CreateMilestonePost(milestone, accountType string) (string, error) {
	momentContent := bs.GenerateRecoveryMoment("milestone", milestone, accountType)
	return bs.CreatePost(momentContent)
}

// GetRecoveryMomentTypes returns types of recovery moments for BeReal
func (bs *BeRealService) GetRecoveryMomentTypes() []struct {
	Type        string
	Title       string
	Description string
	Frequency   string
} {
	return []struct {
		Type        string
		Title       string
		Description string
		Frequency   string
	}{
		{
			Type:        "daily_check_in",
			Title:       "Daily Recovery Check-in",
			Description: "Authentic daily moment in recovery",
			Frequency:   "Daily",
		},
		{
			Type:        "milestone",
			Title:       "Recovery Milestone",
			Description: "Celebrating recovery milestones and achievements",
			Frequency:   "As achieved",
		},
		{
			Type:        "meeting",
			Title:       "Recovery Meeting",
			Description: "Sharing from recovery meetings and community",
			Frequency:   "Weekly",
		},
		{
			Type:        "gratitude",
			Title:       "Gratitude Moment",
			Description: "Expressing gratitude in recovery",
			Frequency:   "Weekly",
		},
		{
			Type:        "challenge",
			Title:       "Challenging Day",
			Description: "Authentic sharing during difficult times",
			Frequency:   "As needed",
		},
		{
			Type:        "service",
			Title:       "Service & Giving Back",
			Description: "Sharing service work and helping others",
			Frequency:   "Monthly",
		},
		{
			Type:        "nature",
			Title:       "Nature & Mindfulness",
			Description: "Finding peace and serenity in nature",
			Frequency:   "Weekly",
		},
		{
			Type:        "workout",
			Title:       "Recovery Fitness",
			Description: "Physical health as part of recovery",
			Frequency:   "Weekly",
		},
	}
}

// GetRecoveryMilestones returns common recovery milestones
func (bs *BeRealService) GetRecoveryMilestones() []string {
	return []string{
		"24 hours",
		"1 week",
		"30 days",
		"60 days",
		"90 days",
		"6 months",
		"1 year",
		"18 months",
		"2 years",
		"3 years",
		"5 years",
		"10 years",
		"15 years",
		"20 years",
		"25+ years",
	}
}

// IsBeRealMomentActive checks if there's an active BeReal moment
func (bs *BeRealService) IsBeRealMomentActive() (bool, *BeRealMoment, error) {
	moment, err := bs.GetCurrentMoment()
	if err != nil {
		return false, nil, err
	}

	// Check if moment is still active
	now := time.Now()
	isActive := moment.IsActive && now.Before(moment.ExpiresAt)

	return isActive, moment, nil
}

// CreateAuthenticRecoveryPost creates an authentic recovery post for the current moment
func (bs *BeRealService) CreateAuthenticRecoveryPost(momentType, milestone, location, accountType string) (string, error) {
	// Check if there's an active moment
	isActive, _, err := bs.IsBeRealMomentActive()
	if err != nil {
		return "", fmt.Errorf("failed to check moment status: %w", err)
	}

	if !isActive {
		return "", fmt.Errorf("no active BeReal moment available")
	}

	// Generate recovery content
	postContent := bs.GenerateRecoveryMoment(momentType, milestone, accountType)

	// Add location if provided
	if location != "" {
		postContent.Location = location
	}

	// Create the post
	postID, err := bs.CreatePost(postContent)
	if err != nil {
		return "", fmt.Errorf("failed to create recovery post: %w", err)
	}

	return postID, nil
}

// GetRecoveryPostsAnalytics retrieves analytics specifically for recovery posts
func (bs *BeRealService) GetRecoveryPostsAnalytics(days int) (map[string]interface{}, error) {
	// Get all posts
	posts, err := bs.GetUserPosts(100)
	if err != nil {
		return nil, err
	}

	// Filter recovery posts from the specified time period
	var recoveryPosts []BeRealPost
	cutoffTime := time.Now().AddDate(0, 0, -days)

	for _, post := range posts {
		if post.IsRecoveryPost && post.CreatedAt.After(cutoffTime) {
			recoveryPosts = append(recoveryPosts, post)
		}
	}

	// Calculate recovery-specific metrics
	var totalReactions, totalComments int
	milestoneCount := 0
	onTimeCount := 0

	for _, post := range recoveryPosts {
		totalReactions += post.ReactionCount
		totalComments += post.CommentCount

		if post.RecoveryMilestone != "" {
			milestoneCount++
		}

		if !post.IsLate {
			onTimeCount++
		}
	}

	// Calculate engagement rate
	engagementRate := float64(0)
	if len(recoveryPosts) > 0 {
		avgEngagement := float64(totalReactions+totalComments) / float64(len(recoveryPosts))
		engagementRate = avgEngagement
	}

	analytics := map[string]interface{}{
		"recovery_posts_count":   len(recoveryPosts),
		"milestone_posts":        milestoneCount,
		"total_reactions":        totalReactions,
		"total_comments":         totalComments,
		"average_engagement":     engagementRate,
		"on_time_percentage":     float64(onTimeCount) / float64(len(recoveryPosts)) * 100,
		"recovery_authenticity":  95.5, // Mock metric for authentic recovery sharing
		"community_impact_score": 87.3, // Mock metric for community impact
	}

	return analytics, nil
}
