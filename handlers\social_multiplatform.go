package handlers

import (
	"net/http"
	"strconv"

	"recovery-dashboard/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// MultiPlatformSocialHandler handles multiple social media platforms
type MultiPlatformSocialHandler struct {
	db               *gorm.DB
	snapchatService  *services.SnapchatService
	threadsService   *services.ThreadsService
	xService         *services.XService
	instagramService *services.InstagramService
	pinterestService *services.PinterestService
}

// NewMultiPlatformSocialHandler creates a new multi-platform social handler
func NewMultiPlatformSocialHandler(db *gorm.DB, snapchat *services.SnapchatService, threads *services.ThreadsService, x *services.XService, instagram *services.InstagramService, pinterest *services.PinterestService) *MultiPlatformSocialHandler {
	return &MultiPlatformSocialHandler{
		db:               db,
		snapchatService:  snapchat,
		threadsService:   threads,
		xService:         x,
		instagramService: instagram,
		pinterestService: pinterest,
	}
}

// GetAllPlatformsStatus returns the status of all social media platforms
func (h *MultiPlatformSocialHandler) GetAllPlatformsStatus(c *gin.Context) {
	status := make(map[string]interface{})

	// Snapchat status
	if h.snapchatService != nil {
		snapStatus := map[string]interface{}{
			"platform":   "snapchat",
			"name":       "Recovery Merch",
			"username":   "@recoverymerch",
			"configured": true,
			"url":        "https://www.snapchat.com/add/recoverymerch",
		}

		err := h.snapchatService.ValidateToken()
		snapStatus["token_valid"] = err == nil
		if err != nil {
			snapStatus["error"] = err.Error()
		}

		status["snapchat"] = snapStatus
	} else {
		status["snapchat"] = map[string]interface{}{
			"platform":   "snapchat",
			"configured": false,
			"error":      "No token configured",
		}
	}

	// Threads status
	if h.threadsService != nil {
		threadsStatus := map[string]interface{}{
			"platform":   "threads",
			"name":       "Recovery Merch",
			"username":   "@recoverymerch",
			"configured": true,
		}

		err := h.threadsService.ValidateToken()
		threadsStatus["token_valid"] = err == nil
		if err != nil {
			threadsStatus["error"] = err.Error()
		}

		status["threads"] = threadsStatus
	} else {
		status["threads"] = map[string]interface{}{
			"platform":   "threads",
			"configured": false,
			"error":      "No token configured",
		}
	}

	// X (Twitter) status
	if h.xService != nil {
		xStatus := map[string]interface{}{
			"platform":   "x",
			"name":       "Recovery Merch",
			"username":   "@recoverymerch",
			"configured": true,
			"url":        "https://x.com/recoverymerch",
		}

		err := h.xService.ValidateToken()
		xStatus["token_valid"] = err == nil
		if err != nil {
			xStatus["error"] = err.Error()
		}

		status["x"] = xStatus
	} else {
		status["x"] = map[string]interface{}{
			"platform":   "x",
			"configured": false,
			"error":      "No token configured",
		}
	}

	// Instagram status
	if h.instagramService != nil {
		instagramStatus := map[string]interface{}{
			"platform":   "instagram",
			"name":       "Recovery Merch",
			"username":   "@recoverymerch",
			"configured": true,
			"url":        "https://www.instagram.com/recoverymerch",
		}

		err := h.instagramService.ValidateToken()
		instagramStatus["token_valid"] = err == nil
		if err != nil {
			instagramStatus["error"] = err.Error()
		}

		status["instagram"] = instagramStatus
	} else {
		status["instagram"] = map[string]interface{}{
			"platform":   "instagram",
			"configured": false,
			"error":      "No token configured",
		}
	}

	// Pinterest status
	if h.pinterestService != nil {
		pinterestStatus := map[string]interface{}{
			"platform":   "pinterest",
			"name":       "Recovery Merch",
			"username":   "@recoverymerch",
			"configured": true,
			"url":        "https://www.pinterest.com/recoverymerch",
		}

		err := h.pinterestService.ValidateToken()
		pinterestStatus["token_valid"] = err == nil
		if err != nil {
			pinterestStatus["error"] = err.Error()
		}

		status["pinterest"] = pinterestStatus
	} else {
		status["pinterest"] = map[string]interface{}{
			"platform":   "pinterest",
			"configured": false,
			"error":      "No token configured",
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"platforms": status,
		"count":     len(status),
		"status":    "success",
	})
}

// CrossPostToAllPlatforms posts content to all configured social media platforms
func (h *MultiPlatformSocialHandler) CrossPostToAllPlatforms(c *gin.Context) {
	var req struct {
		VideoID     string   `json:"video_id" binding:"required"`
		VideoTitle  string   `json:"video_title" binding:"required"`
		VideoURL    string   `json:"video_url" binding:"required"`
		AccountType string   `json:"account_type"` // "merch", "memes", "serious"
		Platforms   []string `json:"platforms"`    // Optional: specific platforms to post to
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "merch"
	}

	// If no specific platforms requested, use all available
	if len(req.Platforms) == 0 {
		req.Platforms = []string{"snapchat", "threads", "x", "instagram", "pinterest"}
	}

	results := make(map[string]interface{})

	// Post to Snapchat
	if contains(req.Platforms, "snapchat") && h.snapchatService != nil {
		storyID, err := h.snapchatService.CrossPostYouTubeVideo(req.VideoTitle, req.VideoURL, req.AccountType)
		if err != nil {
			results["snapchat"] = gin.H{
				"success": false,
				"error":   err.Error(),
			}
		} else {
			results["snapchat"] = gin.H{
				"success":  true,
				"story_id": storyID,
				"platform": "snapchat",
			}
		}
	}

	// Post to Threads
	if contains(req.Platforms, "threads") && h.threadsService != nil {
		postID, err := h.threadsService.CrossPostYouTubeVideo(req.VideoTitle, req.VideoURL, req.AccountType)
		if err != nil {
			results["threads"] = gin.H{
				"success": false,
				"error":   err.Error(),
			}
		} else {
			results["threads"] = gin.H{
				"success":  true,
				"post_id":  postID,
				"platform": "threads",
			}
		}
	}

	// Post to X (Twitter)
	if contains(req.Platforms, "x") && h.xService != nil {
		tweetID, err := h.xService.CrossPostYouTubeVideo(req.VideoTitle, req.VideoURL, req.AccountType)
		if err != nil {
			results["x"] = gin.H{
				"success": false,
				"error":   err.Error(),
			}
		} else {
			results["x"] = gin.H{
				"success":  true,
				"tweet_id": tweetID,
				"platform": "x",
			}
		}
	}

	// Post to Instagram
	if contains(req.Platforms, "instagram") && h.instagramService != nil {
		postID, err := h.instagramService.CrossPostYouTubeVideo(req.VideoTitle, req.VideoURL, req.AccountType)
		if err != nil {
			results["instagram"] = gin.H{
				"success": false,
				"error":   err.Error(),
			}
		} else {
			results["instagram"] = gin.H{
				"success":  true,
				"post_id":  postID,
				"platform": "instagram",
			}
		}
	}

	// Post to Pinterest
	if contains(req.Platforms, "pinterest") && h.pinterestService != nil {
		pinID, err := h.pinterestService.CrossPostYouTubeVideo(req.VideoTitle, req.VideoURL, req.AccountType)
		if err != nil {
			results["pinterest"] = gin.H{
				"success": false,
				"error":   err.Error(),
			}
		} else {
			results["pinterest"] = gin.H{
				"success":  true,
				"pin_id":   pinID,
				"platform": "pinterest",
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success":      true,
		"video_id":     req.VideoID,
		"account_type": req.AccountType,
		"results":      results,
		"message":      "Cross-posting completed",
	})
}

// GetPlatformAnalytics retrieves analytics for all platforms
func (h *MultiPlatformSocialHandler) GetPlatformAnalytics(c *gin.Context) {
	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		days = 30
	}

	analytics := make(map[string]interface{})

	// Snapchat analytics
	if h.snapchatService != nil {
		snapAnalytics, err := h.snapchatService.GetAnalytics(days)
		if err == nil {
			analytics["snapchat"] = snapAnalytics
		} else {
			analytics["snapchat"] = gin.H{"error": err.Error()}
		}
	}

	// Threads analytics
	if h.threadsService != nil {
		threadsAnalytics, err := h.threadsService.GetAnalytics(days)
		if err == nil {
			analytics["threads"] = threadsAnalytics
		} else {
			analytics["threads"] = gin.H{"error": err.Error()}
		}
	}

	// X analytics
	if h.xService != nil {
		xAnalytics, err := h.xService.GetAnalytics(days)
		if err == nil {
			analytics["x"] = xAnalytics
		} else {
			analytics["x"] = gin.H{"error": err.Error()}
		}
	}

	// Instagram analytics
	if h.instagramService != nil {
		instagramAnalytics, err := h.instagramService.GetAnalytics(days)
		if err == nil {
			analytics["instagram"] = instagramAnalytics
		} else {
			analytics["instagram"] = gin.H{"error": err.Error()}
		}
	}

	// Pinterest analytics
	if h.pinterestService != nil {
		pinterestAnalytics, err := h.pinterestService.GetAnalytics(days)
		if err == nil {
			analytics["pinterest"] = pinterestAnalytics
		} else {
			analytics["pinterest"] = gin.H{"error": err.Error()}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"analytics":   analytics,
		"period_days": days,
	})
}

// GenerateContentPreview generates previews for all platforms
func (h *MultiPlatformSocialHandler) GenerateContentPreview(c *gin.Context) {
	var req struct {
		VideoTitle  string `json:"video_title" binding:"required"`
		VideoURL    string `json:"video_url" binding:"required"`
		AccountType string `json:"account_type"` // "merch", "memes", "serious"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request: " + err.Error()})
		return
	}

	// Set default account type
	if req.AccountType == "" {
		req.AccountType = "merch"
	}

	previews := make(map[string]interface{})

	// Snapchat preview
	if h.snapchatService != nil {
		snapPreview := h.snapchatService.GenerateRecoveryStory(req.VideoTitle, req.VideoURL, req.AccountType)
		previews["snapchat"] = gin.H{
			"caption":    snapPreview.Caption,
			"media_type": snapPreview.MediaType,
			"duration":   snapPreview.Duration,
		}
	}

	// Threads preview
	if h.threadsService != nil {
		threadsPreview := h.threadsService.GenerateRecoveryPost(req.VideoTitle, req.VideoURL, req.AccountType)
		previews["threads"] = gin.H{
			"text":       threadsPreview.Text,
			"media_type": threadsPreview.MediaType,
		}
	}

	// X preview
	if h.xService != nil {
		xPreview := h.xService.GenerateRecoveryTweet(req.VideoTitle, req.VideoURL, req.AccountType)
		previews["x"] = gin.H{
			"text":       xPreview.Text,
			"char_count": len(xPreview.Text),
		}
	}

	// Instagram preview
	if h.instagramService != nil {
		instagramPreview := h.instagramService.GenerateRecoveryPost(req.VideoTitle, req.VideoURL, req.AccountType)
		previews["instagram"] = gin.H{
			"caption":    instagramPreview.Caption,
			"media_type": instagramPreview.MediaType,
			"char_count": len(instagramPreview.Caption),
		}
	}

	// Pinterest preview
	if h.pinterestService != nil {
		pinterestPreview := h.pinterestService.GenerateRecoveryPin(req.VideoTitle, req.VideoURL, req.AccountType)
		previews["pinterest"] = gin.H{
			"title":       pinterestPreview.Title,
			"description": pinterestPreview.Description,
			"char_count":  len(pinterestPreview.Description),
			"link":        pinterestPreview.Link,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"previews":     previews,
		"account_type": req.AccountType,
		"message":      "Content previews generated successfully",
	})
}

// Helper function to check if slice contains string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
