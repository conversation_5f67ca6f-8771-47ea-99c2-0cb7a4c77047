package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// PinterestService handles Pinterest API interactions
type PinterestService struct {
	AccessToken string
	BaseURL     string
}

// NewPinterestService creates a new Pinterest service instance
func NewPinterestService(accessToken string) *PinterestService {
	return &PinterestService{
		AccessToken: accessToken,
		BaseURL:     "https://api.pinterest.com/v5",
	}
}

// PinterestUser represents Pinterest user information
type PinterestUser struct {
	ID              string `json:"id"`
	Username        string `json:"username"`
	FirstName       string `json:"first_name"`
	LastName        string `json:"last_name"`
	DisplayName     string `json:"display_name"`
	Bio             string `json:"bio"`
	CreatedAt       string `json:"created_at"`
	PinCount        int    `json:"pin_count"`
	BoardCount      int    `json:"board_count"`
	FollowerCount   int    `json:"follower_count"`
	FollowingCount  int    `json:"following_count"`
	MonthlyViews    int    `json:"monthly_views"`
	Website         string `json:"website"`
	ProfileImageURL string `json:"profile_image"`
}

// PinterestBoard represents a Pinterest board
type PinterestBoard struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Privacy     string    `json:"privacy"` // PUBLIC, PROTECTED, SECRET
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	PinCount    int       `json:"pin_count"`
	URL         string    `json:"url"`
}

// PinterestPin represents a Pinterest pin
type PinterestPin struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Link        string    `json:"link"`
	MediaURL    string    `json:"media_url"`
	BoardID     string    `json:"board_id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	PinMetrics  struct {
		Impressions int `json:"impressions"`
		Saves       int `json:"saves"`
		PinClicks   int `json:"pin_clicks"`
		Outbound    int `json:"outbound"`
	} `json:"pin_metrics"`
}

// PinterestAnalytics represents Pinterest analytics data
type PinterestAnalytics struct {
	Impressions    int            `json:"impressions"`
	Saves          int            `json:"saves"`
	PinClicks      int            `json:"pin_clicks"`
	OutboundClicks int            `json:"outbound_clicks"`
	ProfileViews   int            `json:"profile_views"`
	MonthlyViews   int            `json:"monthly_views"`
	EngagementRate float64        `json:"engagement_rate"`
	TopPins        []PinterestPin `json:"top_pins"`
}

// PinterestCreateBoardRequest represents a request to create a Pinterest board
type PinterestCreateBoardRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Privacy     string `json:"privacy"` // PUBLIC, PROTECTED, SECRET
}

// PinterestCreatePinRequest represents a request to create a Pinterest pin
type PinterestCreatePinRequest struct {
	BoardID     string `json:"board_id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Link        string `json:"link"`
	MediaURL    string `json:"media_url"`
	MediaSource struct {
		SourceType string `json:"source_type"` // image_url, video_url
		URL        string `json:"url"`
	} `json:"media_source"`
}

// GetUserInfo retrieves Pinterest user information
func (ps *PinterestService) GetUserInfo() (*PinterestUser, error) {
	url := fmt.Sprintf("%s/user_account", ps.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+ps.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Pinterest API error: %s", string(body))
	}

	var user PinterestUser
	if err := json.NewDecoder(resp.Body).Decode(&user); err != nil {
		return nil, fmt.Errorf("failed to decode user info: %w", err)
	}

	return &user, nil
}

// GetBoards retrieves user's Pinterest boards
func (ps *PinterestService) GetBoards() ([]PinterestBoard, error) {
	url := fmt.Sprintf("%s/boards", ps.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+ps.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get boards: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Pinterest API error: %s", string(body))
	}

	var response struct {
		Items []PinterestBoard `json:"items"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode boards: %w", err)
	}

	return response.Items, nil
}

// CreateBoard creates a new Pinterest board
func (ps *PinterestService) CreateBoard(req PinterestCreateBoardRequest) (string, error) {
	url := fmt.Sprintf("%s/boards", ps.BaseURL)

	jsonBody, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+ps.AccessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to create board: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Pinterest API error: %s", string(body))
	}

	var response struct {
		ID string `json:"id"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	return response.ID, nil
}

// CreatePin creates a new Pinterest pin
func (ps *PinterestService) CreatePin(req PinterestCreatePinRequest) (string, error) {
	url := fmt.Sprintf("%s/pins", ps.BaseURL)

	jsonBody, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+ps.AccessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to create pin: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("Pinterest API error: %s", string(body))
	}

	var response struct {
		ID string `json:"id"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	return response.ID, nil
}

// GetBoardPins retrieves pins from a specific board
func (ps *PinterestService) GetBoardPins(boardID string, limit int) ([]PinterestPin, error) {
	url := fmt.Sprintf("%s/boards/%s/pins?page_size=%d", ps.BaseURL, boardID, limit)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+ps.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get pins: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("Pinterest API error: %s", string(body))
	}

	var response struct {
		Items []PinterestPin `json:"items"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, fmt.Errorf("failed to decode pins: %w", err)
	}

	return response.Items, nil
}

// GetAnalytics retrieves Pinterest analytics
func (ps *PinterestService) GetAnalytics(days int) (*PinterestAnalytics, error) {
	// Mock analytics for now since Pinterest analytics require business account
	analytics := &PinterestAnalytics{
		Impressions:    45600,
		Saves:          1250,
		PinClicks:      890,
		OutboundClicks: 456,
		ProfileViews:   2100,
		MonthlyViews:   125000,
		EngagementRate: 3.8,
		TopPins:        []PinterestPin{}, // Would be populated with actual data
	}

	return analytics, nil
}

// ValidateToken checks if the Pinterest access token is valid
func (ps *PinterestService) ValidateToken() error {
	_, err := ps.GetUserInfo()
	return err
}

// GenerateRecoveryPin creates Pinterest-appropriate content from YouTube videos
func (ps *PinterestService) GenerateRecoveryPin(videoTitle, videoURL, accountType string) PinterestCreatePinRequest {
	var title, description string

	switch accountType {
	case "merch":
		title = fmt.Sprintf("Recovery Gear: %s", videoTitle)
		description = fmt.Sprintf("🔥 Recovery content + merchandise that represents your journey!\n\n%s\n\nShop recovery gear that matters 🙏\n\n#Recovery #RecoveryMerch #Sobriety #OneDayAtATime #RecoveryGear #SoberStyle #RecoveryJourney #Hope #Healing #AA #NA #RecoveryCommunity #SoundOfRecovery", videoTitle)
	case "memes":
		title = fmt.Sprintf("Recovery Humor: %s", videoTitle)
		description = fmt.Sprintf("Recovery mood 😅\n\n%s\n\nWhen the program hits different 💯\n\nSave this for later! 📌\n\n#RecoveryMemes #SoberLife #RecoveryHumor #Recovery #Sobriety #OneDayAtATime #RecoveryJourney #AA #NA #RecoveryCommunity #SoundOfRecovery", videoTitle)
	case "serious":
		title = fmt.Sprintf("Recovery Wisdom: %s", videoTitle)
		description = fmt.Sprintf("Powerful recovery wisdom 🙏\n\n%s\n\nEvery story matters. Every day counts. Keep going.\n\nSave for inspiration 💫\n\n#RecoveryWisdom #Inspiration #Recovery #Hope #Healing #Sobriety #OneDayAtATime #RecoveryJourney #AA #NA #RecoveryCommunity #SoundOfRecovery", videoTitle)
	default:
		title = fmt.Sprintf("Recovery Content: %s", videoTitle)
		description = fmt.Sprintf("Recovery inspiration:\n\n%s\n\nYour recovery story matters 🙏\n\n#Recovery #Sobriety #OneDayAtATime #RecoveryJourney #Hope #Healing #AA #NA #RecoveryCommunity #SoundOfRecovery", videoTitle)
	}

	// Pinterest allows up to 500 characters for descriptions
	if len(description) > 500 {
		description = description[:497] + "..."
	}

	return PinterestCreatePinRequest{
		Title:       title,
		Description: description,
		Link:        videoURL,
		MediaSource: struct {
			SourceType string `json:"source_type"`
			URL        string `json:"url"`
		}{
			SourceType: "image_url",
			URL:        "", // This would be populated with generated image URL
		},
	}
}

// CreateRecoveryQuotePin creates a recovery quote pin with generated visual
func (ps *PinterestService) CreateRecoveryQuotePin(quote, author, imageURL, boardID, accountType string) (string, error) {
	var title, description string

	switch accountType {
	case "merch":
		title = fmt.Sprintf("Recovery Quote: %s", author)
		description = fmt.Sprintf("💫 Recovery wisdom for your journey\n\n\"%s\" - %s\n\nRecovery gear available 🙏\n\n#RecoveryQuotes #RecoveryMerch #Recovery #Inspiration", quote, author)
	case "serious":
		title = fmt.Sprintf("Wisdom from %s", author)
		description = fmt.Sprintf("🙏 Words of wisdom\n\n\"%s\" - %s\n\nReflect on this today 💭\n\n#RecoveryQuotes #RecoveryWisdom #Recovery #Inspiration", quote, author)
	default:
		title = fmt.Sprintf("Recovery Quote: %s", author)
		description = fmt.Sprintf("\"%s\" - %s\n\n#RecoveryQuotes #Recovery #Inspiration #Hope #Healing", quote, author)
	}

	pinReq := PinterestCreatePinRequest{
		BoardID:     boardID,
		Title:       title,
		Description: description,
		MediaSource: struct {
			SourceType string `json:"source_type"`
			URL        string `json:"url"`
		}{
			SourceType: "image_url",
			URL:        imageURL,
		},
	}

	return ps.CreatePin(pinReq)
}

// CrossPostYouTubeVideo creates a Pinterest pin for a YouTube video
func (ps *PinterestService) CrossPostYouTubeVideo(videoTitle, videoURL, accountType string) (string, error) {
	pinContent := ps.GenerateRecoveryPin(videoTitle, videoURL, accountType)

	// For cross-posting, we'll need to generate or use a thumbnail image
	// This would typically involve creating a visual representation of the video
	// For now, we'll return an error indicating that media URL and board ID are required
	if pinContent.MediaSource.URL == "" || pinContent.BoardID == "" {
		return "", fmt.Errorf("media URL and board ID required for Pinterest pin - visual content generation and board selection needed")
	}

	return ps.CreatePin(pinContent)
}

// GetOrCreateRecoveryBoards ensures recovery-themed boards exist
func (ps *PinterestService) GetOrCreateRecoveryBoards() (map[string]string, error) {
	boards := map[string]string{
		"recovery_wisdom":  "Recovery Wisdom",
		"recovery_quotes":  "Recovery Quotes",
		"recovery_stories": "Recovery Stories",
		"recovery_merch":   "Recovery Merchandise",
		"recovery_humor":   "Recovery Humor",
	}

	existingBoards, err := ps.GetBoards()
	if err != nil {
		return nil, fmt.Errorf("failed to get existing boards: %w", err)
	}

	boardIDs := make(map[string]string)

	// Check which boards already exist
	for key, boardName := range boards {
		found := false
		for _, existing := range existingBoards {
			if existing.Name == boardName {
				boardIDs[key] = existing.ID
				found = true
				break
			}
		}

		// Create board if it doesn't exist
		if !found {
			boardReq := PinterestCreateBoardRequest{
				Name:        boardName,
				Description: fmt.Sprintf("Recovery content focused on %s - inspiring stories and wisdom for the recovery journey", boardName),
				Privacy:     "PUBLIC",
			}

			boardID, err := ps.CreateBoard(boardReq)
			if err != nil {
				return nil, fmt.Errorf("failed to create board %s: %w", boardName, err)
			}

			boardIDs[key] = boardID
		}
	}

	return boardIDs, nil
}
