package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// LinkedInService handles LinkedIn API interactions
type LinkedInService struct {
	AccessToken string
	BaseURL     string
}

// NewLinkedInService creates a new LinkedIn service instance
func NewLinkedInService(accessToken string) *LinkedInService {
	return &LinkedInService{
		AccessToken: accessToken,
		BaseURL:     "https://api.linkedin.com/v2",
	}
}

// LinkedInProfile represents LinkedIn profile information
type LinkedInProfile struct {
	ID                   string `json:"id"`
	FirstName            string `json:"firstName"`
	LastName             string `json:"lastName"`
	Headline             string `json:"headline"`
	Summary              string `json:"summary"`
	Industry             string `json:"industry"`
	Location             string `json:"location"`
	NumConnections       int    `json:"numConnections"`
	NumConnectionsCapped bool   `json:"numConnectionsCapped"`
	ProfilePictureURL    string `json:"profilePictureUrl"`
	PublicProfileURL     string `json:"publicProfileUrl"`
}

// LinkedInPost represents a LinkedIn post/share
type LinkedInPost struct {
	ID             string    `json:"id"`
	Text           string    `json:"text"`
	Author         string    `json:"author"`
	CreatedTime    time.Time `json:"createdTime"`
	UpdatedTime    time.Time `json:"updatedTime"`
	Visibility     string    `json:"visibility"`     // PUBLIC, CONNECTIONS, LOGGED_IN_MEMBERS
	LifecycleState string    `json:"lifecycleState"` // PUBLISHED, DRAFT
	Content        struct {
		ContentEntities []struct {
			Entity         string `json:"entity"`
			EntityLocation string `json:"entityLocation"`
		} `json:"contentEntities"`
		Title       string `json:"title"`
		Description string `json:"description"`
	} `json:"content"`
	Commentary string `json:"commentary"`
}

// LinkedInArticle represents a LinkedIn article
type LinkedInArticle struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Content     string    `json:"content"`
	Author      string    `json:"author"`
	PublishedAt time.Time `json:"publishedAt"`
	UpdatedAt   time.Time `json:"updatedAt"`
	Visibility  string    `json:"visibility"`
	State       string    `json:"state"`
	Views       int       `json:"views"`
	Likes       int       `json:"likes"`
	Comments    int       `json:"comments"`
	Shares      int       `json:"shares"`
}

// LinkedInAnalytics represents LinkedIn analytics data
type LinkedInAnalytics struct {
	ProfileViews      int               `json:"profile_views"`
	PostImpressions   int               `json:"post_impressions"`
	PostEngagements   int               `json:"post_engagements"`
	ArticleViews      int               `json:"article_views"`
	SearchAppearances int               `json:"search_appearances"`
	ConnectionsGrowth int               `json:"connections_growth"`
	EngagementRate    float64           `json:"engagement_rate"`
	TopPosts          []LinkedInPost    `json:"top_posts"`
	TopArticles       []LinkedInArticle `json:"top_articles"`
	IndustryRanking   int               `json:"industry_ranking"`
	InfluenceScore    float64           `json:"influence_score"`
}

// LinkedInShareRequest represents a request to create a LinkedIn post
type LinkedInShareRequest struct {
	Author      string `json:"author"`
	Text        string `json:"text"`
	Visibility  string `json:"visibility"` // PUBLIC, CONNECTIONS
	ContentURL  string `json:"content_url,omitempty"`
	Title       string `json:"title,omitempty"`
	Description string `json:"description,omitempty"`
	ImageURL    string `json:"image_url,omitempty"`
}

// LinkedInArticleRequest represents a request to create a LinkedIn article
type LinkedInArticleRequest struct {
	Title      string   `json:"title"`
	Content    string   `json:"content"`
	Visibility string   `json:"visibility"` // PUBLIC, CONNECTIONS
	Tags       []string `json:"tags,omitempty"`
	CoverImage string   `json:"cover_image,omitempty"`
}

// GetProfile retrieves LinkedIn profile information
func (ls *LinkedInService) GetProfile() (*LinkedInProfile, error) {
	url := fmt.Sprintf("%s/people/~", ls.BaseURL)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+ls.AccessToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get profile: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("LinkedIn API error: %s", string(body))
	}

	var profile LinkedInProfile
	if err := json.NewDecoder(resp.Body).Decode(&profile); err != nil {
		return nil, fmt.Errorf("failed to decode profile: %w", err)
	}

	return &profile, nil
}

// CreatePost creates a LinkedIn post/share
func (ls *LinkedInService) CreatePost(req LinkedInShareRequest) (string, error) {
	url := fmt.Sprintf("%s/shares", ls.BaseURL)

	// Build the share request according to LinkedIn API v2
	shareData := map[string]interface{}{
		"author":         fmt.Sprintf("urn:li:person:%s", req.Author),
		"lifecycleState": "PUBLISHED",
		"specificContent": map[string]interface{}{
			"com.linkedin.ugc.ShareContent": map[string]interface{}{
				"shareCommentary": map[string]interface{}{
					"text": req.Text,
				},
				"shareMediaCategory": "NONE",
			},
		},
		"visibility": map[string]interface{}{
			"com.linkedin.ugc.MemberNetworkVisibility": req.Visibility,
		},
	}

	// Add content if URL is provided
	if req.ContentURL != "" {
		shareData["specificContent"].(map[string]interface{})["com.linkedin.ugc.ShareContent"].(map[string]interface{})["shareMediaCategory"] = "ARTICLE"
		shareData["specificContent"].(map[string]interface{})["com.linkedin.ugc.ShareContent"].(map[string]interface{})["media"] = []map[string]interface{}{
			{
				"status": "READY",
				"description": map[string]interface{}{
					"text": req.Description,
				},
				"originalUrl": req.ContentURL,
				"title": map[string]interface{}{
					"text": req.Title,
				},
			},
		}
	}

	jsonBody, err := json.Marshal(shareData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+ls.AccessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to create post: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("LinkedIn API error: %s", string(body))
	}

	var response struct {
		ID string `json:"id"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	return response.ID, nil
}

// CreateArticle creates a LinkedIn article
func (ls *LinkedInService) CreateArticle(req LinkedInArticleRequest) (string, error) {
	url := fmt.Sprintf("%s/articles", ls.BaseURL)

	articleData := map[string]interface{}{
		"title":      req.Title,
		"content":    req.Content,
		"visibility": req.Visibility,
		"state":      "PUBLISHED",
	}

	if len(req.Tags) > 0 {
		articleData["tags"] = req.Tags
	}

	if req.CoverImage != "" {
		articleData["coverImage"] = req.CoverImage
	}

	jsonBody, err := json.Marshal(articleData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	httpReq, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Authorization", "Bearer "+ls.AccessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to create article: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("LinkedIn API error: %s", string(body))
	}

	var response struct {
		ID string `json:"id"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	return response.ID, nil
}

// GetAnalytics retrieves LinkedIn analytics (mock implementation)
func (ls *LinkedInService) GetAnalytics(days int) (*LinkedInAnalytics, error) {
	// Mock analytics for now - in production, this would use LinkedIn Analytics API
	analytics := &LinkedInAnalytics{
		ProfileViews:      1250,
		PostImpressions:   8900,
		PostEngagements:   456,
		ArticleViews:      2340,
		SearchAppearances: 567,
		ConnectionsGrowth: 23,
		EngagementRate:    5.1,
		TopPosts:          []LinkedInPost{},    // Would be populated with actual data
		TopArticles:       []LinkedInArticle{}, // Would be populated with actual data
		IndustryRanking:   15,
		InfluenceScore:    78.5,
	}

	return analytics, nil
}

// ValidateToken checks if the LinkedIn access token is valid
func (ls *LinkedInService) ValidateToken() error {
	_, err := ls.GetProfile()
	return err
}

// GenerateRecoveryPost creates LinkedIn-appropriate professional content from YouTube videos
func (ls *LinkedInService) GenerateRecoveryPost(videoTitle, videoURL, authorID, accountType string) LinkedInShareRequest {
	var text string

	switch accountType {
	case "merch":
		text = fmt.Sprintf("🌟 Sharing valuable recovery content and resources\n\n%s\n\nAs someone passionate about supporting the recovery community, I believe in the power of shared experiences and authentic stories. This content resonates with the professional challenges many of us face in recovery.\n\nRecovery isn't just personal—it's professional. It's about showing up authentically in all areas of our lives.\n\n🔗 Watch: %s\n\n#Recovery #ProfessionalDevelopment #MentalHealth #Authenticity #Leadership #RecoveryCommunity", videoTitle, videoURL)

	case "serious":
		text = fmt.Sprintf("💼 Professional Recovery Advocacy\n\n%s\n\nIn my professional journey, I've learned that recovery and career success aren't mutually exclusive—they're complementary. This content speaks to the intersection of personal growth and professional excellence.\n\nBreaking stigma in the workplace starts with authentic leadership and open conversations about mental health and recovery.\n\n🎥 Resource: %s\n\n#RecoveryAdvocacy #WorkplaceWellness #MentalHealthAwareness #ProfessionalGrowth #Leadership #Authenticity", videoTitle, videoURL)

	default:
		text = fmt.Sprintf("🎯 Recovery & Professional Excellence\n\n%s\n\nSharing this valuable content that bridges personal recovery and professional success. The skills we develop in recovery—resilience, authenticity, emotional intelligence—are the same skills that make us effective leaders and colleagues.\n\n📺 %s\n\n#Recovery #ProfessionalDevelopment #Leadership #MentalHealth #WorkplaceWellness", videoTitle, videoURL)
	}

	return LinkedInShareRequest{
		Author:      authorID,
		Text:        text,
		Visibility:  "PUBLIC",
		ContentURL:  videoURL,
		Title:       videoTitle,
		Description: "Recovery content for professional development and authentic leadership",
	}
}

// CrossPostYouTubeVideo creates a LinkedIn post for a YouTube video
func (ls *LinkedInService) CrossPostYouTubeVideo(videoTitle, videoURL, authorID, accountType string) (string, error) {
	postContent := ls.GenerateRecoveryPost(videoTitle, videoURL, authorID, accountType)
	return ls.CreatePost(postContent)
}

// CreateRecoveryArticle creates a professional recovery article
func (ls *LinkedInService) CreateRecoveryArticle(title, topic, accountType string) (string, error) {
	var content string
	var tags []string

	switch topic {
	case "workplace_recovery":
		title = "Recovery in the Workplace: Breaking Stigma Through Authentic Leadership"
		content = `
# Recovery in the Workplace: Breaking Stigma Through Authentic Leadership

Recovery and professional success aren't just compatible—they're synergistic. As professionals in recovery, we bring unique strengths to the workplace that can transform organizational culture and drive meaningful results.

## The Hidden Strengths of Recovery

People in recovery develop exceptional skills that translate directly to professional excellence:

- **Emotional Intelligence**: Recovery teaches us to understand and manage our emotions effectively
- **Resilience**: We've learned to bounce back from setbacks and adapt to change
- **Authenticity**: Recovery demands honesty, which builds trust in professional relationships
- **Problem-Solving**: We've developed creative solutions to complex personal challenges
- **Empathy**: Our experiences help us connect with and support colleagues

## Breaking the Stigma

The stigma around addiction and recovery in the workplace is real, but it's changing. Forward-thinking organizations recognize that:

1. Diversity includes neurodiversity and recovery experiences
2. Authentic leadership requires vulnerability and honesty
3. Mental health support benefits everyone, not just those in recovery
4. Recovery stories inspire resilience across teams

## Creating Supportive Workplaces

As professionals, we can advocate for:

- Employee assistance programs that include recovery support
- Mental health days and flexible work arrangements
- Open conversations about wellness and authenticity
- Leadership development that includes emotional intelligence
- Policies that support rather than penalize recovery

## The Business Case for Recovery Support

Organizations that support recovery see:

- Increased employee engagement and loyalty
- Reduced healthcare costs
- Improved team dynamics and communication
- Enhanced innovation through diverse perspectives
- Stronger leadership pipeline

## Moving Forward

Recovery isn't something to hide—it's a source of strength. By sharing our stories and advocating for supportive workplace cultures, we create environments where everyone can thrive authentically.

The future of work is authentic, empathetic, and inclusive. Those of us in recovery are uniquely positioned to lead this transformation.

---

*What has your recovery journey taught you about professional excellence? Share your thoughts in the comments.*
`
		tags = []string{"Recovery", "Leadership", "WorkplaceWellness", "MentalHealth", "Authenticity", "ProfessionalDevelopment"}

	case "leadership_recovery":
		title = "The Recovery Advantage: How Personal Growth Transforms Professional Leadership"
		content = `
# The Recovery Advantage: How Personal Growth Transforms Professional Leadership

Recovery isn't just about overcoming addiction—it's about developing the most essential leadership skills of our time. The journey of recovery creates leaders who are authentic, resilient, and deeply connected to their purpose.

## Recovery as Leadership Training

The recovery process is essentially leadership development in disguise:

### Self-Awareness
Recovery begins with honest self-assessment. This same skill helps leaders understand their strengths, weaknesses, and impact on others.

### Accountability
In recovery, we learn to take responsibility for our actions and their consequences. This translates to leaders who own their decisions and learn from mistakes.

### Emotional Regulation
Managing triggers and emotions in recovery develops the emotional intelligence that great leaders need to navigate complex situations.

### Authentic Communication
Recovery requires honest, vulnerable communication. These skills create leaders who build trust through transparency.

## The Competitive Advantage

Leaders in recovery often outperform their peers because they:

- Make decisions based on values, not ego
- Build genuine connections with team members
- Navigate crises with calm and clarity
- Inspire others through authentic vulnerability
- Focus on long-term success over short-term gains

## Transforming Organizational Culture

Recovery-informed leadership creates:

- Psychologically safe environments where people can be authentic
- Cultures of continuous learning and growth
- Teams that support each other through challenges
- Organizations that prioritize well-being alongside performance
- Workplaces where diversity of experience is valued

## The Ripple Effect

When leaders share their recovery stories appropriately, it:

- Reduces stigma around mental health and addiction
- Encourages others to seek help when needed
- Creates permission for vulnerability and authenticity
- Builds stronger, more connected teams
- Attracts top talent who value authentic leadership

## Leading with Purpose

Recovery gives leaders a clear sense of purpose beyond profit. This purpose-driven leadership:

- Inspires teams to connect with meaningful work
- Drives innovation that serves the greater good
- Creates sustainable business practices
- Builds organizations that make a positive impact

The skills developed in recovery—honesty, humility, resilience, empathy—are exactly what modern organizations need in their leaders.

---

*How has your personal growth journey influenced your leadership style? Let's continue this important conversation.*
`
		tags = []string{"Leadership", "Recovery", "PersonalGrowth", "Authenticity", "EmotionalIntelligence", "PurposeDriven"}

	case "mental_health":
		title = "Mental Health in Professional Settings: A Recovery Perspective"
		content = `
# Mental Health in Professional Settings: A Recovery Perspective

Mental health isn't a personal issue that we leave at home—it's a professional asset that, when properly supported, drives innovation, creativity, and authentic leadership in the workplace.

## Reframing Mental Health at Work

Traditional workplace culture often treats mental health as:
- A liability to be managed
- A personal problem to be hidden
- A sign of weakness or instability

Recovery-informed workplaces recognize mental health as:
- A source of unique perspectives and creativity
- An opportunity for authentic leadership
- A driver of innovation and problem-solving
- A foundation for resilient teams

## The Business Impact

Organizations that prioritize mental health see:

### Increased Productivity
- Employees who feel supported are more engaged
- Reduced absenteeism and presenteeism
- Higher quality work from mentally healthy teams

### Enhanced Innovation
- Diverse mental health experiences drive creative solutions
- Psychological safety encourages risk-taking and innovation
- Authentic teams collaborate more effectively

### Improved Retention
- Employees stay with organizations that support their whole selves
- Reduced recruitment and training costs
- Stronger employer brand and reputation

## Creating Mentally Healthy Workplaces

Practical steps organizations can take:

1. **Leadership Modeling**: Leaders who share their mental health journeys create permission for others
2. **Policy Changes**: Flexible work arrangements, mental health days, and comprehensive EAP programs
3. **Training Programs**: Mental health first aid and stigma reduction training for managers
4. **Safe Spaces**: Employee resource groups and peer support networks
5. **Holistic Benefits**: Mental health coverage that includes therapy, coaching, and recovery support

## The Recovery Advantage

Professionals in recovery bring unique value:

- **Crisis Management**: Experience navigating personal crises translates to professional resilience
- **Emotional Intelligence**: Recovery develops deep self-awareness and empathy
- **Authentic Leadership**: Vulnerability and honesty build trust and connection
- **Growth Mindset**: Continuous personal development drives professional excellence
- **Purpose-Driven Work**: Clear values and purpose create meaningful contributions

## Breaking Down Barriers

To create truly inclusive workplaces, we must:

- Challenge stigma through education and open dialogue
- Share stories that humanize mental health experiences
- Advocate for policies that support mental wellness
- Create cultures where seeking help is seen as strength
- Recognize that mental health affects everyone, not just those with diagnoses

## The Future of Work

The future workplace will be:
- Authentically human, not just professionally polished
- Supportive of whole-person wellness
- Inclusive of diverse mental health experiences
- Led by emotionally intelligent leaders
- Focused on sustainable success, not just short-term gains

Mental health isn't a barrier to professional success—it's a pathway to more authentic, innovative, and impactful work.

---

*What changes would you like to see in how workplaces approach mental health? Share your thoughts and experiences.*
`
		tags = []string{"MentalHealth", "WorkplaceWellness", "Recovery", "Inclusion", "Leadership", "Innovation"}

	default:
		title = "Recovery and Professional Excellence: A Personal Journey"
		content = `
# Recovery and Professional Excellence: A Personal Journey

Recovery has taught me that personal growth and professional success aren't separate journeys—they're interconnected paths that strengthen and support each other.

## The Intersection of Personal and Professional

In recovery, I've learned that the skills that help us stay sober are the same skills that make us exceptional professionals:

- **Honesty** builds trust with colleagues and clients
- **Accountability** creates reliable leadership
- **Resilience** helps us navigate workplace challenges
- **Empathy** strengthens team relationships
- **Purpose** drives meaningful work

## Lessons from Recovery

Recovery has taught me valuable professional lessons:

### Authenticity Over Perfection
Trying to appear perfect is exhausting and unsustainable. Authentic leadership, including acknowledging our struggles and growth, creates deeper connections and more effective teams.

### Progress Over Perfection
Recovery is about progress, not perfection. This mindset helps in professional settings where continuous improvement matters more than flawless execution.

### Community and Support
Just as recovery requires community, professional success is enhanced by strong relationships, mentorship, and mutual support.

### Values-Based Decision Making
Recovery clarifies our values. Making professional decisions based on these values creates more fulfilling and sustainable careers.

## The Ripple Effect

When we bring our authentic selves to work, including our recovery journey, we:

- Give others permission to be authentic
- Create psychologically safe environments
- Build stronger, more connected teams
- Drive innovation through diverse perspectives
- Contribute to positive organizational culture

## Challenges and Opportunities

While there can be challenges in sharing our recovery story professionally, the opportunities often outweigh the risks:

**Challenges:**
- Potential stigma or discrimination
- Concerns about professional image
- Uncertainty about when and how to share

**Opportunities:**
- Authentic leadership and connection
- Inspiring others who may be struggling
- Contributing to positive workplace culture
- Building trust through vulnerability
- Creating meaningful impact beyond profit

## Moving Forward

Recovery isn't something to overcome and forget—it's an ongoing journey that continues to teach us valuable lessons about resilience, authenticity, and purpose.

As professionals in recovery, we have the opportunity to lead by example, showing that personal struggles can become professional strengths, and that authenticity is the foundation of exceptional leadership.

---

*How has your personal journey influenced your professional path? I'd love to hear your story.*
`
		tags = []string{"Recovery", "ProfessionalDevelopment", "Authenticity", "Leadership", "PersonalGrowth"}
	}

	articleReq := LinkedInArticleRequest{
		Title:      title,
		Content:    content,
		Visibility: "PUBLIC",
		Tags:       tags,
	}

	return ls.CreateArticle(articleReq)
}

// GetRecoveryTopics returns professional recovery topics for LinkedIn content
func (ls *LinkedInService) GetRecoveryTopics() []struct {
	Topic       string
	Title       string
	Description string
	Audience    string
} {
	return []struct {
		Topic       string
		Title       string
		Description string
		Audience    string
	}{
		{
			Topic:       "workplace_recovery",
			Title:       "Recovery in the Workplace",
			Description: "Breaking stigma and creating supportive work environments",
			Audience:    "HR professionals, managers, executives",
		},
		{
			Topic:       "leadership_recovery",
			Title:       "Recovery-Informed Leadership",
			Description: "How personal growth transforms professional leadership",
			Audience:    "Leaders, managers, entrepreneurs",
		},
		{
			Topic:       "mental_health",
			Title:       "Mental Health in Professional Settings",
			Description: "Creating mentally healthy and inclusive workplaces",
			Audience:    "All professionals, HR, wellness advocates",
		},
		{
			Topic:       "authenticity",
			Title:       "Authentic Professional Identity",
			Description: "Bringing your whole self to work, including recovery experience",
			Audience:    "Professionals in recovery, advocates",
		},
		{
			Topic:       "resilience",
			Title:       "Professional Resilience Through Recovery",
			Description: "How recovery builds unshakeable professional resilience",
			Audience:    "Business leaders, career coaches",
		},
	}
}
